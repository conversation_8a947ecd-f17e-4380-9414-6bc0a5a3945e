
[0;35m[SECTION][0m 15:27:15 AdaS 批量内存分配处理开始
=====================================
[0;34m[INFO][0m 15:27:15 工作目录: /workspace/toolchain/work/adas_batch
[0;34m[INFO][0m 15:27:15 执行目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:27:15 自动探测 kernel 目录...
[0;34m[INFO][0m 15:27:15 检查路径: /workspace/triton_ada_dnn/models/ada1
[0;34m[INFO][0m 15:27:15 检查路径: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1
[0;32m[SUCCESS][0m 15:27:15 找到 kernel 目录: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1

[0;35m[SECTION][0m 15:27:15 设置环境变量
=====================================
[0;34m[INFO][0m 15:27:15 TRITON_ALWAYS_COMPILE=1
[0;34m[INFO][0m 15:27:15 ADA_MEMPLAN_VISUALIZE=1
[0;34m[INFO][0m 15:27:15 ADA_MEMPLAN_EXPORT_ADAS_INPUT=1
[0;34m[INFO][0m 15:27:15 ADA_OR_TOOLS_TIMEOUT=180
[0;34m[INFO][0m 15:27:15 PYTHONPATH=/workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib

[0;35m[SECTION][0m 15:27:15 检查工具路径
=====================================
[0;32m[SUCCESS][0m 15:27:15 工具存在: adas_memplan.py
[0;32m[SUCCESS][0m 15:27:15 工具存在: adas_memplan_cp_sat.py
[0;32m[SUCCESS][0m 15:27:15 工具存在: adas_memory_visualizer_adas_dual.py
[0;34m[INFO][0m 15:27:15 内存容量配置: Local=3145728B (3MB), Weight=16777216B (16MB)
[0;34m[INFO][0m 15:27:15 求解参数: Fast=60s, Optimal=300s, Workers=0

[0;35m[SECTION][0m 15:27:15 扫描 kernel 文件
=====================================
[0;32m[SUCCESS][0m 15:27:15 发现 17 个 kernel 文件:
[0;34m[INFO][0m 15:27:15   1. m8_14b/attn_gemm
[0;34m[INFO][0m 15:27:15   2. m8_14b/ffn
[0;34m[INFO][0m 15:27:15   3. m8_14b/qkv_gemm
[0;34m[INFO][0m 15:27:15   4. m8_14b/self_atten
[0;34m[INFO][0m 15:27:15   5. m8_14b/sp2tp8_flash_attention
[0;34m[INFO][0m 15:27:15   6. m8_14b/sp2tp8_native_attention_pipeline
[0;34m[INFO][0m 15:27:15   7. m8_14b/test_bandwidth_selfattn_sp2tp8
[0;34m[INFO][0m 15:27:15   8. m8_14b/test_short_load_store_selfattn_sp2tp8
[0;34m[INFO][0m 15:27:15   9. x2i/x2i_atten_kernel
[0;34m[INFO][0m 15:27:15   10. x2i/x2i_atten_sp2_kernel
[0;34m[INFO][0m 15:27:15   11. x2v/x2v_atten_gemm_ffn_txt
[0;34m[INFO][0m 15:27:15   12. x2v/x2v_qkv_gemm_rope_txt
[0;34m[INFO][0m 15:27:15   13. x2v/x2v_qkv_gemm_rope_v0
[0;34m[INFO][0m 15:27:15   14. x2v/x2v_qkv_gemm_rope_vid_v1
[0;34m[INFO][0m 15:27:16   15. x2v/x2v_qkv_gemm_rope_vid_v2
[0;34m[INFO][0m 15:27:16   16. x2v/x2v_space_2d_atten
[0;34m[INFO][0m 15:27:16   17. x2v/x2v_window_atten

[0;35m[SECTION][0m 15:27:16 开始批量处理
=====================================

[0;35m[SECTION][0m 15:27:16 [1/17] 处理 m8_14b/attn_gemm
=====================================
[0;34m[INFO][0m 15:27:16 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/attn_gemm.py
[0;34m[INFO][0m 15:27:16 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/attn_gemm.py
[0;34m[INFO][0m 15:27:16 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:27:16 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:27:16 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/attn_gemm.py:537: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(0)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m
[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeRelax → conflicts: 47 (Bank:14 Region:33), score: 7.533723e+03
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 40 个逻辑分配, 40 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 90 对, Region冲突 188 对, 总计 278 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:27:23.991864 154850 ada_task.cc:233] ###### finish task, AIU elasped time is:724548 ns, pe start index is:0
I0810 15:27:24.231696 154850 ada_task.cc:233] ###### finish task, AIU elasped time is:723546 ns, pe start index is:0
I0810 15:27:24.444151 154850 ada_task.cc:233] ###### finish task, AIU elasped time is:727130 ns, pe start index is:0
aot bdmodel path is: /root/.triton/cache/AMSMKTLOTIWOHBKXR46JFS2F4FYAWQMK5TQUGAIHPMCW2JNOQWYA/gen_bdmodel/out.bdmodel
latency(us) is 727.13
ATTN GEMM Test Done!
[0;34m[INFO][0m 15:27:24 执行完成，检查输出...
[0;34m[INFO][0m 15:27:24 kernel 执行完成 (8s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:27:24 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:27:24   找到JSON: /tmp/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:27:24 处理 JSON: memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:27:24   执行贪心算法...
[0;34m[INFO][0m 15:27:24     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=40/40 failed=0
[adas_memplan] memory-summary: original_total=39.22 MB, scaled_total=93.15 MB, local_peak=2.43 MB, weight_peak=15.53 MB
[0;32m[SUCCESS][0m 15:27:25     贪心算法成功
[0;34m[INFO][0m 15:27:25     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:27:25     贪心可视化成功
[0;34m[INFO][0m 15:27:25   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:27:25     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=40/40 failed=0
[adas_memplan_cp] memory-summary: original_total=39.22 MB, scaled_total=93.15 MB, local_peak=2.38 MB, weight_peak=15.57 MB
[0;32m[SUCCESS][0m 15:27:25     OR-Tools fast 成功
[0;34m[INFO][0m 15:27:25     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:27:25     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:27:25   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:27:25     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=40/40 failed=0
[adas_memplan_cp] memory-summary: original_total=39.22 MB, scaled_total=93.15 MB, local_peak=2.38 MB, weight_peak=15.57 MB
[0;32m[SUCCESS][0m 15:27:26     OR-Tools optimal 成功
[0;34m[INFO][0m 15:27:26     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__attn_gemm/memory_layout_debug_tmpb01gmx4d.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 15:27:26     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 15:27:26 进度: 1/17, 已用时: 10s, 预计剩余: 160s

[0;35m[SECTION][0m 15:27:26 [2/17] 处理 m8_14b/ffn
=====================================
[0;34m[INFO][0m 15:27:26 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/ffn.py
[0;34m[INFO][0m 15:27:26 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/ffn.py
[0;34m[INFO][0m 15:27:26 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:27:26 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:27:26 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
/usr/lib/python3.9/site-packages/adahub/modules/base_module.py:26: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(0)
---- batch = 1024 ----
Autotuning kernel ffn_pipe_batch_kernel with config PIPE_BLOCK_SIZE: -1, LOAD_BEFORE_TIMES: 16, VEC_TILE_NUM_0: 1, VEC_TILE_NUM_1: 1, VEC_TILE_NUM_2: 1, num_warps: 4, num_ctas: 1, num_stages: 3, maxnreg: None
kernel used: macro_ffn_kernel_single

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  17%|[37m█▋        [0m| stage-2[36mDEBUG: Using selector: EpollSelector[0m

compiling:  33%|[37m███▎      [0m| stage-4[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 0 (Bank:0 Region:0), score: 5.516876e+02
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 32 个逻辑分配, 32 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 74 对, Region冲突 140 对, 总计 214 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:27:34.233511 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265236 ns, pe start index is:0
I0810 15:27:34.233914 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263609 ns, pe start index is:0
I0810 15:27:34.234264 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263445 ns, pe start index is:0
I0810 15:27:34.234607 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263916 ns, pe start index is:0
I0810 15:27:34.234947 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264271 ns, pe start index is:0
I0810 15:27:34.236049 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277628 ns, pe start index is:0
I0810 15:27:34.236410 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264210 ns, pe start index is:0
I0810 15:27:34.236753 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264029 ns, pe start index is:0
I0810 15:27:34.237095 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264555 ns, pe start index is:0
I0810 15:27:34.237437 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264527 ns, pe start index is:0
I0810 15:27:34.237789 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265659 ns, pe start index is:0
I0810 15:27:34.238132 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264468 ns, pe start index is:0
I0810 15:27:34.238476 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264601 ns, pe start index is:0
I0810 15:27:34.238816 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264579 ns, pe start index is:0
I0810 15:27:34.239187 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264373 ns, pe start index is:0
I0810 15:27:34.239675 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263987 ns, pe start index is:0
I0810 15:27:34.240046 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277355 ns, pe start index is:0
I0810 15:27:34.240543 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264059 ns, pe start index is:0
I0810 15:27:34.240891 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263736 ns, pe start index is:0
I0810 15:27:34.241243 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263876 ns, pe start index is:0
I0810 15:27:34.241593 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263705 ns, pe start index is:0
I0810 15:27:34.241936 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263975 ns, pe start index is:0
I0810 15:27:34.242280 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264672 ns, pe start index is:0
I0810 15:27:34.242633 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263610 ns, pe start index is:0
I0810 15:27:34.242972 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263942 ns, pe start index is:0
I0810 15:27:34.243314 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264369 ns, pe start index is:0
I0810 15:27:34.243651 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263322 ns, pe start index is:0
I0810 15:27:34.244009 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:276015 ns, pe start index is:0
I0810 15:27:34.245813 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264243 ns, pe start index is:0
I0810 15:27:34.246151 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263738 ns, pe start index is:0
I0810 15:27:34.246491 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264346 ns, pe start index is:0
I0810 15:27:34.246832 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263522 ns, pe start index is:0
I0810 15:27:34.247170 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264742 ns, pe start index is:0
I0810 15:27:34.247530 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263648 ns, pe start index is:0
I0810 15:27:34.247876 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264450 ns, pe start index is:0
I0810 15:27:34.248240 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:270951 ns, pe start index is:0
I0810 15:27:34.248562 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263028 ns, pe start index is:0
I0810 15:27:34.248883 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264109 ns, pe start index is:0
I0810 15:27:34.249221 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263454 ns, pe start index is:0
I0810 15:27:34.249588 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263806 ns, pe start index is:0
I0810 15:27:34.249939 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264348 ns, pe start index is:0
I0810 15:27:34.250279 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264471 ns, pe start index is:0
I0810 15:27:34.250627 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264667 ns, pe start index is:0
I0810 15:27:34.250977 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263604 ns, pe start index is:0
I0810 15:27:34.251320 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263597 ns, pe start index is:0
I0810 15:27:34.251688 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263498 ns, pe start index is:0
I0810 15:27:34.252048 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:276347 ns, pe start index is:0
I0810 15:27:34.252403 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264145 ns, pe start index is:0
I0810 15:27:34.252741 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264024 ns, pe start index is:0
I0810 15:27:34.253082 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264806 ns, pe start index is:0
I0810 15:27:34.253435 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263988 ns, pe start index is:0
I0810 15:27:34.253839 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263544 ns, pe start index is:0
I0810 15:27:34.254293 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264390 ns, pe start index is:0
I0810 15:27:34.254642 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264166 ns, pe start index is:0
I0810 15:27:34.254976 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264029 ns, pe start index is:0
I0810 15:27:34.255306 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264092 ns, pe start index is:0
I0810 15:27:34.255626 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263594 ns, pe start index is:0
I0810 15:27:34.255959 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264300 ns, pe start index is:0
I0810 15:27:34.256321 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264153 ns, pe start index is:0
I0810 15:27:34.256666 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263751 ns, pe start index is:0
I0810 15:27:34.257010 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264165 ns, pe start index is:0
I0810 15:27:34.257350 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263627 ns, pe start index is:0
I0810 15:27:34.257699 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264483 ns, pe start index is:0
I0810 15:27:34.258026 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264349 ns, pe start index is:0
I0810 15:27:34.258348 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263475 ns, pe start index is:0
I0810 15:27:34.258668 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264023 ns, pe start index is:0
I0810 15:27:34.258988 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265063 ns, pe start index is:0
I0810 15:27:34.259310 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264555 ns, pe start index is:0
I0810 15:27:34.259630 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264569 ns, pe start index is:0
I0810 15:27:34.259955 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263825 ns, pe start index is:0
I0810 15:27:34.260291 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264395 ns, pe start index is:0
I0810 15:27:34.260638 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263661 ns, pe start index is:0
I0810 15:27:34.260965 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263239 ns, pe start index is:0
I0810 15:27:34.261292 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262931 ns, pe start index is:0
I0810 15:27:34.261615 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264373 ns, pe start index is:0
I0810 15:27:34.261938 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264449 ns, pe start index is:0
I0810 15:27:34.262264 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263711 ns, pe start index is:0
I0810 15:27:34.262585 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263209 ns, pe start index is:0
I0810 15:27:34.262907 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263655 ns, pe start index is:0
I0810 15:27:34.263228 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264013 ns, pe start index is:0
I0810 15:27:34.263551 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263427 ns, pe start index is:0
I0810 15:27:34.263871 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263893 ns, pe start index is:0
I0810 15:27:34.264209 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:271621 ns, pe start index is:0
I0810 15:27:34.264529 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264037 ns, pe start index is:0
I0810 15:27:34.264850 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263684 ns, pe start index is:0
I0810 15:27:34.265172 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264489 ns, pe start index is:0
I0810 15:27:34.265496 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264686 ns, pe start index is:0
I0810 15:27:34.265822 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264647 ns, pe start index is:0
I0810 15:27:34.266151 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263927 ns, pe start index is:0
I0810 15:27:34.266477 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264137 ns, pe start index is:0
I0810 15:27:34.266803 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264093 ns, pe start index is:0
I0810 15:27:34.267125 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265222 ns, pe start index is:0
I0810 15:27:34.267449 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264150 ns, pe start index is:0
I0810 15:27:34.267772 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264393 ns, pe start index is:0
I0810 15:27:34.268105 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:275732 ns, pe start index is:0
I0810 15:27:34.268427 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263522 ns, pe start index is:0
I0810 15:27:34.268749 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264047 ns, pe start index is:0
I0810 15:27:34.269070 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263913 ns, pe start index is:0
I0810 15:27:34.269393 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264361 ns, pe start index is:0
I0810 15:27:34.269716 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265158 ns, pe start index is:0
I0810 15:27:34.270038 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264066 ns, pe start index is:0
I0810 15:27:34.270364 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265037 ns, pe start index is:0
I0810 15:27:34.270694 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264110 ns, pe start index is:0
I0810 15:27:34.271019 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264685 ns, pe start index is:0
I0810 15:27:34.271342 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264209 ns, pe start index is:0
I0810 15:27:34.271663 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264072 ns, pe start index is:0
I0810 15:27:34.271984 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264096 ns, pe start index is:0
I0810 15:27:34.272320 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264237 ns, pe start index is:0
I0810 15:27:34.272640 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263507 ns, pe start index is:0
I0810 15:27:34.272961 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264145 ns, pe start index is:0
I0810 15:27:34.273281 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263365 ns, pe start index is:0
I0810 15:27:34.273602 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263653 ns, pe start index is:0
I0810 15:27:34.273928 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264223 ns, pe start index is:0
I0810 15:27:34.274250 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263869 ns, pe start index is:0
I0810 15:27:34.274573 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263685 ns, pe start index is:0
I0810 15:27:34.274894 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264349 ns, pe start index is:0
I0810 15:27:34.275216 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263658 ns, pe start index is:0
I0810 15:27:34.275539 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264107 ns, pe start index is:0
I0810 15:27:34.275862 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263989 ns, pe start index is:0
I0810 15:27:34.276194 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:273826 ns, pe start index is:0
I0810 15:27:34.276526 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264826 ns, pe start index is:0
I0810 15:27:34.276849 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263839 ns, pe start index is:0
I0810 15:27:34.277169 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264143 ns, pe start index is:0
I0810 15:27:34.277493 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264161 ns, pe start index is:0
I0810 15:27:34.277813 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264279 ns, pe start index is:0
I0810 15:27:34.278136 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264909 ns, pe start index is:0
I0810 15:27:34.278460 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264097 ns, pe start index is:0
I0810 15:27:34.278781 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264307 ns, pe start index is:0
I0810 15:27:34.279106 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264773 ns, pe start index is:0
I0810 15:27:34.279436 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264275 ns, pe start index is:0
I0810 15:27:34.279759 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264579 ns, pe start index is:0
I0810 15:27:34.280090 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:273892 ns, pe start index is:0
I0810 15:27:34.280414 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264992 ns, pe start index is:0
I0810 15:27:34.280736 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264164 ns, pe start index is:0
I0810 15:27:34.281059 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264288 ns, pe start index is:0
I0810 15:27:34.281385 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264132 ns, pe start index is:0
I0810 15:27:34.281708 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264512 ns, pe start index is:0
I0810 15:27:34.282032 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264158 ns, pe start index is:0
I0810 15:27:34.282354 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264064 ns, pe start index is:0
I0810 15:27:34.282676 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263758 ns, pe start index is:0
I0810 15:27:34.282999 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265122 ns, pe start index is:0
I0810 15:27:34.283322 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264130 ns, pe start index is:0
I0810 15:27:34.283644 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264364 ns, pe start index is:0
I0810 15:27:34.283965 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263792 ns, pe start index is:0
I0810 15:27:34.284302 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277538 ns, pe start index is:0
I0810 15:27:34.284627 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264233 ns, pe start index is:0
I0810 15:27:34.284948 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264853 ns, pe start index is:0
I0810 15:27:34.285301 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264235 ns, pe start index is:0
I0810 15:27:34.285791 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263715 ns, pe start index is:0
I0810 15:27:34.286178 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264559 ns, pe start index is:0
I0810 15:27:34.286615 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264731 ns, pe start index is:0
I0810 15:27:34.286952 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263765 ns, pe start index is:0
I0810 15:27:34.287283 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263785 ns, pe start index is:0
I0810 15:27:34.287614 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264743 ns, pe start index is:0
I0810 15:27:34.287953 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264119 ns, pe start index is:0
I0810 15:27:34.288297 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:274819 ns, pe start index is:0
I0810 15:27:34.288628 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264902 ns, pe start index is:0
I0810 15:27:34.288959 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264332 ns, pe start index is:0
I0810 15:27:34.289302 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264874 ns, pe start index is:0
I0810 15:27:34.289633 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264284 ns, pe start index is:0
I0810 15:27:34.289963 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264542 ns, pe start index is:0
I0810 15:27:34.290297 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263910 ns, pe start index is:0
I0810 15:27:34.290629 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264309 ns, pe start index is:0
I0810 15:27:34.290975 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264480 ns, pe start index is:0
I0810 15:27:34.291335 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264918 ns, pe start index is:0
I0810 15:27:34.291661 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264686 ns, pe start index is:0
I0810 15:27:34.291983 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264088 ns, pe start index is:0
I0810 15:27:34.292321 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277186 ns, pe start index is:0
I0810 15:27:34.292644 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264125 ns, pe start index is:0
I0810 15:27:34.292965 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262892 ns, pe start index is:0
I0810 15:27:34.293288 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264344 ns, pe start index is:0
I0810 15:27:34.293612 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263811 ns, pe start index is:0
I0810 15:27:34.293933 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263446 ns, pe start index is:0
I0810 15:27:34.294255 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263314 ns, pe start index is:0
I0810 15:27:34.294579 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263468 ns, pe start index is:0
I0810 15:27:34.294900 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263900 ns, pe start index is:0
I0810 15:27:34.295229 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264228 ns, pe start index is:0
I0810 15:27:34.295550 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263658 ns, pe start index is:0
I0810 15:27:34.295872 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263140 ns, pe start index is:0
I0810 15:27:34.296206 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:274894 ns, pe start index is:0
I0810 15:27:34.296535 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263417 ns, pe start index is:0
I0810 15:27:34.296859 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265387 ns, pe start index is:0
I0810 15:27:34.297180 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264050 ns, pe start index is:0
I0810 15:27:34.297501 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263525 ns, pe start index is:0
I0810 15:27:34.297823 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263515 ns, pe start index is:0
I0810 15:27:34.298146 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264138 ns, pe start index is:0
I0810 15:27:34.298468 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263854 ns, pe start index is:0
I0810 15:27:34.298794 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264393 ns, pe start index is:0
I0810 15:27:34.299120 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264592 ns, pe start index is:0
I0810 15:27:34.299443 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264057 ns, pe start index is:0
I0810 15:27:34.299763 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263819 ns, pe start index is:0
I0810 15:27:34.300088 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264081 ns, pe start index is:0
I0810 15:27:34.300413 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263017 ns, pe start index is:0
I0810 15:27:34.300735 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263345 ns, pe start index is:0
I0810 15:27:34.301057 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264059 ns, pe start index is:0
I0810 15:27:34.301378 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263058 ns, pe start index is:0
I0810 15:27:34.301700 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263022 ns, pe start index is:0
I0810 15:27:34.302023 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263173 ns, pe start index is:0
I0810 15:27:34.302345 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262792 ns, pe start index is:0
I0810 15:27:34.302667 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262517 ns, pe start index is:0
I0810 15:27:34.302989 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262997 ns, pe start index is:0
I0810 15:27:34.303313 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262891 ns, pe start index is:0
I0810 15:27:34.303632 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263127 ns, pe start index is:0
I0810 15:27:34.303953 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262963 ns, pe start index is:0
I0810 15:27:34.304282 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:270481 ns, pe start index is:0
I0810 15:27:34.304603 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263013 ns, pe start index is:0
I0810 15:27:34.304924 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262924 ns, pe start index is:0
I0810 15:27:34.305246 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263548 ns, pe start index is:0
I0810 15:27:34.305567 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263481 ns, pe start index is:0
I0810 15:27:34.305887 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263700 ns, pe start index is:0
I0810 15:27:34.306216 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263430 ns, pe start index is:0
I0810 15:27:34.306538 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263046 ns, pe start index is:0
I0810 15:27:34.306859 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263364 ns, pe start index is:0
I0810 15:27:34.307178 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263418 ns, pe start index is:0
I0810 15:27:34.307499 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262766 ns, pe start index is:0
I0810 15:27:34.307821 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262808 ns, pe start index is:0
I0810 15:27:34.308156 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:278107 ns, pe start index is:0
I0810 15:27:34.308476 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262655 ns, pe start index is:0
I0810 15:27:34.308797 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263702 ns, pe start index is:0
I0810 15:27:34.309118 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262866 ns, pe start index is:0
I0810 15:27:34.309440 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263818 ns, pe start index is:0
I0810 15:27:34.309759 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263488 ns, pe start index is:0
I0810 15:27:34.310079 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262768 ns, pe start index is:0
I0810 15:27:34.310400 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262234 ns, pe start index is:0
I0810 15:27:34.310720 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262634 ns, pe start index is:0
I0810 15:27:34.311040 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263484 ns, pe start index is:0
I0810 15:27:34.311360 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262932 ns, pe start index is:0
I0810 15:27:34.311682 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263784 ns, pe start index is:0
I0810 15:27:34.312003 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263042 ns, pe start index is:0
I0810 15:27:34.312331 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:268235 ns, pe start index is:0
I0810 15:27:34.312654 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263700 ns, pe start index is:0
I0810 15:27:34.312976 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263835 ns, pe start index is:0
I0810 15:27:34.313299 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264367 ns, pe start index is:0
I0810 15:27:34.313620 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263815 ns, pe start index is:0
I0810 15:27:34.313942 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263637 ns, pe start index is:0
I0810 15:27:34.314265 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263345 ns, pe start index is:0
I0810 15:27:34.314587 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264125 ns, pe start index is:0
I0810 15:27:34.314908 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263447 ns, pe start index is:0
I0810 15:27:34.315233 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263679 ns, pe start index is:0
I0810 15:27:34.315557 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263491 ns, pe start index is:0
I0810 15:27:34.315882 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263723 ns, pe start index is:0
I0810 15:27:34.316241 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:273529 ns, pe start index is:0
I0810 15:27:34.316708 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263944 ns, pe start index is:0
I0810 15:27:34.317058 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263529 ns, pe start index is:0
I0810 15:27:34.317409 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264071 ns, pe start index is:0
I0810 15:27:34.317760 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263413 ns, pe start index is:0
I0810 15:27:34.318126 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264785 ns, pe start index is:0
I0810 15:27:34.318625 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264097 ns, pe start index is:0
I0810 15:27:34.319005 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264637 ns, pe start index is:0
I0810 15:27:34.319355 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263935 ns, pe start index is:0
I0810 15:27:34.319697 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263899 ns, pe start index is:0
I0810 15:27:34.320040 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263215 ns, pe start index is:0
I0810 15:27:34.320394 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:270407 ns, pe start index is:0
I0810 15:27:34.320737 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264049 ns, pe start index is:0
I0810 15:27:34.321061 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264488 ns, pe start index is:0
I0810 15:27:34.321384 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264566 ns, pe start index is:0
I0810 15:27:34.321709 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264088 ns, pe start index is:0
I0810 15:27:34.322031 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264038 ns, pe start index is:0
I0810 15:27:34.322360 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264056 ns, pe start index is:0
I0810 15:27:34.322680 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263872 ns, pe start index is:0
I0810 15:27:34.323002 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264652 ns, pe start index is:0
I0810 15:27:34.323325 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264232 ns, pe start index is:0
I0810 15:27:34.323648 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263786 ns, pe start index is:0
I0810 15:27:34.323974 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264340 ns, pe start index is:0
I0810 15:27:34.324303 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:270511 ns, pe start index is:0
I0810 15:27:34.324625 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264396 ns, pe start index is:0
I0810 15:27:34.324945 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264111 ns, pe start index is:0
I0810 15:27:34.325268 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264961 ns, pe start index is:0
I0810 15:27:34.325590 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265025 ns, pe start index is:0
I0810 15:27:34.325911 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264629 ns, pe start index is:0
I0810 15:27:34.326233 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264387 ns, pe start index is:0
I0810 15:27:34.326556 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264293 ns, pe start index is:0
I0810 15:27:34.326880 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263641 ns, pe start index is:0
I0810 15:27:34.327210 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264941 ns, pe start index is:0
I0810 15:27:34.327533 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264201 ns, pe start index is:0
I0810 15:27:34.327853 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264709 ns, pe start index is:0
I0810 15:27:34.328184 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:273734 ns, pe start index is:0
I0810 15:27:34.328516 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264288 ns, pe start index is:0
I0810 15:27:34.328836 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263368 ns, pe start index is:0
I0810 15:27:34.329159 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264270 ns, pe start index is:0
I0810 15:27:34.329479 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263318 ns, pe start index is:0
I0810 15:27:34.329800 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263498 ns, pe start index is:0
I0810 15:27:34.330121 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264048 ns, pe start index is:0
I0810 15:27:34.331758 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263564 ns, pe start index is:0
I0810 15:27:34.332079 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264278 ns, pe start index is:0
I0810 15:27:34.332415 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277898 ns, pe start index is:0
I0810 15:27:34.332736 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263625 ns, pe start index is:0
I0810 15:27:34.333055 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263714 ns, pe start index is:0
I0810 15:27:34.333408 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263668 ns, pe start index is:0
I0810 15:27:34.333873 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263320 ns, pe start index is:0
I0810 15:27:34.334231 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263548 ns, pe start index is:0
I0810 15:27:34.334578 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:262698 ns, pe start index is:0
I0810 15:27:34.334910 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263634 ns, pe start index is:0
I0810 15:27:34.335232 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263844 ns, pe start index is:0
I0810 15:27:34.335554 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263748 ns, pe start index is:0
I0810 15:27:34.335875 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264118 ns, pe start index is:0
I0810 15:27:34.336210 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:275683 ns, pe start index is:0
I0810 15:27:34.336535 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264183 ns, pe start index is:0
I0810 15:27:34.336858 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263692 ns, pe start index is:0
I0810 15:27:34.337178 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264020 ns, pe start index is:0
I0810 15:27:34.337510 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263842 ns, pe start index is:0
I0810 15:27:34.337834 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263422 ns, pe start index is:0
I0810 15:27:34.338155 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264300 ns, pe start index is:0
I0810 15:27:34.338477 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263316 ns, pe start index is:0
I0810 15:27:34.338829 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263952 ns, pe start index is:0
I0810 15:27:34.339272 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264608 ns, pe start index is:0
I0810 15:27:34.339622 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263292 ns, pe start index is:0
I0810 15:27:34.339972 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263560 ns, pe start index is:0
I0810 15:27:34.340317 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:274591 ns, pe start index is:0
I0810 15:27:34.340641 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264200 ns, pe start index is:0
I0810 15:27:34.340968 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263705 ns, pe start index is:0
I0810 15:27:34.341291 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264313 ns, pe start index is:0
I0810 15:27:34.341615 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264097 ns, pe start index is:0
I0810 15:27:34.341938 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263835 ns, pe start index is:0
I0810 15:27:34.342264 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264255 ns, pe start index is:0
I0810 15:27:34.342586 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264849 ns, pe start index is:0
I0810 15:27:34.342911 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265521 ns, pe start index is:0
I0810 15:27:34.343233 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263551 ns, pe start index is:0
I0810 15:27:34.343556 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264387 ns, pe start index is:0
I0810 15:27:34.343878 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263843 ns, pe start index is:0
I0810 15:27:34.344218 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:278185 ns, pe start index is:0
I0810 15:27:34.344540 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263902 ns, pe start index is:0
I0810 15:27:34.344863 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265454 ns, pe start index is:0
I0810 15:27:34.345185 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264521 ns, pe start index is:0
I0810 15:27:34.345515 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264284 ns, pe start index is:0
I0810 15:27:34.345861 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264550 ns, pe start index is:0
I0810 15:27:34.346326 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264064 ns, pe start index is:0
I0810 15:27:34.346683 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264706 ns, pe start index is:0
I0810 15:27:34.347025 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264520 ns, pe start index is:0
I0810 15:27:34.347368 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265032 ns, pe start index is:0
I0810 15:27:34.347719 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263941 ns, pe start index is:0
I0810 15:27:34.348059 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264486 ns, pe start index is:0
I0810 15:27:34.348426 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:277299 ns, pe start index is:0
I0810 15:27:34.348769 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265105 ns, pe start index is:0
I0810 15:27:34.349109 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264205 ns, pe start index is:0
I0810 15:27:34.349462 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:263773 ns, pe start index is:0
I0810 15:27:34.349813 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264405 ns, pe start index is:0
I0810 15:27:34.350162 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:264793 ns, pe start index is:0
I0810 15:27:34.375645 155958 ada_task.cc:233] ###### finish task, AIU elasped time is:265663 ns, pe start index is:0
Triton autotuning for function ffn_pipe_batch_kernel finished after 3.34s; best config selected: PIPE_BLOCK_SIZE: -1, LOAD_BEFORE_TIMES: 16, VEC_TILE_NUM_0: 1, VEC_TILE_NUM_1: 1, VEC_TILE_NUM_2: 1, num_warps: 4, num_ctas: 1, num_stages: 3, maxnreg: None;
[0;34m[INFO][0m 15:27:34 执行完成，检查输出...
[0;34m[INFO][0m 15:27:34 kernel 执行完成 (8s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:27:34 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:27:34   找到JSON: /tmp/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:27:34 处理 JSON: memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:27:34   执行贪心算法...
[0;34m[INFO][0m 15:27:34     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=32/32 failed=0
[adas_memplan] memory-summary: original_total=16.87 MB, scaled_total=40.08 MB, local_peak=1.51 MB, weight_peak=6.25 MB
[0;32m[SUCCESS][0m 15:27:34     贪心算法成功
[0;34m[INFO][0m 15:27:34     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:27:35     贪心可视化成功
[0;34m[INFO][0m 15:27:35   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:27:35     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=32/32 failed=0
[adas_memplan_cp] memory-summary: original_total=16.87 MB, scaled_total=40.08 MB, local_peak=1.37 MB, weight_peak=6.43 MB
[0;32m[SUCCESS][0m 15:27:35     OR-Tools fast 成功
[0;34m[INFO][0m 15:27:35     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:27:35     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:27:35   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:27:35     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=32/32 failed=0
[adas_memplan_cp] memory-summary: original_total=16.87 MB, scaled_total=40.08 MB, local_peak=1.37 MB, weight_peak=6.43 MB
[0;32m[SUCCESS][0m 15:27:36     OR-Tools optimal 成功
[0;34m[INFO][0m 15:27:36     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__ffn/memory_layout_debug_tmpibg3w872.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 15:27:36     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 15:27:36 进度: 2/17, 已用时: 20s, 预计剩余: 150s

[0;35m[SECTION][0m 15:27:36 [3/17] 处理 m8_14b/qkv_gemm
=====================================
[0;34m[INFO][0m 15:27:36 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/qkv_gemm.py
[0;34m[INFO][0m 15:27:36 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/qkv_gemm.py
[0;34m[INFO][0m 15:27:36 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:27:36 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:27:36 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/qkv_gemm.py:1179: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(0)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m

compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 176 (Bank:108 Region:68), score: 2.425466e+04
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 196 个逻辑分配, 196 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 1750 对, Region冲突 2062 对, 总计 3812 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:27:52.637226 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:787798 ns, pe start index is:0
I0810 15:27:54.671176 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:795897 ns, pe start index is:0
I0810 15:27:56.551482 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:782645 ns, pe start index is:0
I0810 15:27:58.368608 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:781953 ns, pe start index is:0
I0810 15:28:00.129627 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:782871 ns, pe start index is:0
I0810 15:28:01.935274 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:782065 ns, pe start index is:0
I0810 15:28:03.748621 156946 ada_task.cc:233] ###### finish task, AIU elasped time is:805947 ns, pe start index is:0
aot bdmodel path is: /root/.triton/cache/QNPAF2LFHOT4ISGKYPSXL7B2ZQEKPAN5XFZXKMXDMAJ6LFZB6DGQ/gen_bdmodel/out.bdmodel
latency(us) is 805.947
[0;34m[INFO][0m 15:28:05 执行完成，检查输出...
[0;34m[INFO][0m 15:28:05 kernel 执行完成 (29s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:28:05 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:28:05   找到JSON: /tmp/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:05 处理 JSON: memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:05   执行贪心算法...
[0;34m[INFO][0m 15:28:05     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=196/196 failed=0
[adas_memplan] memory-summary: original_total=44.34 MB, scaled_total=105.30 MB, local_peak=1.42 MB, weight_peak=10.70 MB
[0;32m[SUCCESS][0m 15:28:05     贪心算法成功
[0;34m[INFO][0m 15:28:05     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:28:05     贪心可视化成功
[0;34m[INFO][0m 15:28:05   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:28:05     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=196/196 failed=0
[adas_memplan_cp] memory-summary: original_total=44.34 MB, scaled_total=105.30 MB, local_peak=0.82 MB, weight_peak=11.14 MB
[0;32m[SUCCESS][0m 15:28:07     OR-Tools fast 成功
[0;34m[INFO][0m 15:28:07     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:28:07     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:28:07   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:28:07     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=196/196 failed=0
[adas_memplan_cp] memory-summary: original_total=44.34 MB, scaled_total=105.30 MB, local_peak=1.22 MB, weight_peak=10.74 MB
[0;32m[SUCCESS][0m 15:28:08     OR-Tools optimal 成功
[0;34m[INFO][0m 15:28:08     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__qkv_gemm/memory_layout_debug_tmpxl4f_up0.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 15:28:08     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 15:28:08 进度: 3/17, 已用时: 52s, 预计剩余: 238s

[0;35m[SECTION][0m 15:28:08 [4/17] 处理 m8_14b/self_atten
=====================================
[0;34m[INFO][0m 15:28:08 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/self_atten.py
[0;34m[INFO][0m 15:28:08 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/self_atten.py
[0;34m[INFO][0m 15:28:08 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:28:08 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:28:08 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
topo:  1
short_attn_kernel
fp32[constexpr[1024], constexpr[4], constexpr[128]]

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m

compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 1104 (Bank:490 Region:614), score: 1.320423e+05
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 347 个逻辑分配, 347 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 3264 对, Region冲突 4190 对, 总计 7454 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:28:32.932276 158605 ada_task.cc:233] ###### finish task, AIU elasped time is:1808014 ns, pe start index is:0
I0810 15:28:33.150825 158605 ada_task.cc:233] ###### finish task, AIU elasped time is:1785058 ns, pe start index is:0
topo:  1
short_attn_kernel
tensor(2, dtype=torch.int8)
aot bdmodel path is: /root/.triton/cache/LZBK7PBQVAT5HVOS7AZWPB7MMGWBXUBCZLWVSQPARMH2Z2LUMAZQ/gen_bdmodel/out.bdmodel
latency(ms) is 1.785058
[0;34m[INFO][0m 15:28:34 执行完成，检查输出...
[0;34m[INFO][0m 15:28:34 kernel 执行完成 (26s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:28:34 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:28:34   找到JSON: /tmp/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:34 处理 JSON: memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:34   执行贪心算法...
[0;34m[INFO][0m 15:28:34     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=347/347 failed=0
[adas_memplan] memory-summary: original_total=81.18 MB, scaled_total=192.79 MB, local_peak=2.90 MB, weight_peak=13.36 MB
[0;32m[SUCCESS][0m 15:28:34     贪心算法成功
[0;34m[INFO][0m 15:28:34     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:28:34     贪心可视化成功
[0;34m[INFO][0m 15:28:34   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:28:34     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=347/347 failed=0
[adas_memplan_cp] memory-summary: original_total=81.18 MB, scaled_total=192.79 MB, local_peak=2.52 MB, weight_peak=13.74 MB
[0;32m[SUCCESS][0m 15:28:37     OR-Tools fast 成功
[0;34m[INFO][0m 15:28:37     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:28:37     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:28:37   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:28:37     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__self_atten/memory_layout_debug_tmpdv1pkhe1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/347 failed=347
[0;31m[ERROR][0m 15:28:38     OR-Tools optimal 失败
[0;34m[INFO][0m 15:28:38 进度: 4/17, 已用时: 82s, 预计剩余: 260s

[0;35m[SECTION][0m 15:28:38 [5/17] 处理 m8_14b/sp2tp8_flash_attention
=====================================
[0;34m[INFO][0m 15:28:38 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/sp2tp8_flash_attention.py
[0;34m[INFO][0m 15:28:38 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/sp2tp8_flash_attention.py
[0;34m[INFO][0m 15:28:38 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:28:38 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:28:38 开始执行...
==== Triton Kernel Info ====
Tensor Q Shape is torch.Size([1024, 4, 16, 128]), Dtype is torch.float16
Tensor K Shape is torch.Size([4096, 1, 8, 128]), Dtype is torch.int8

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m
func.return %4, %5 : tensor<1024x1x128xf16>, tensor<1024x128xf16>

compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5func.return %4, %5 : tensor<1024x1x128xf16>, tensor<1024x128xf16>
func.return %4, %5 : tensor<1024x1x128xf16>, tensor<1024x128xf16>
[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeRelax → conflicts: 346 (Bank:120 Region:226), score: 3.962945e+04
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 333 个逻辑分配, 333 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 656 对, Region冲突 2054 对, 总计 2710 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:28:53.834730 160105 ada_task.cc:233] ###### finish task, AIU elasped time is:4177104 ns, pe start index is:0
[0;34m[INFO][0m 15:28:54 执行完成，检查输出...
[0;34m[INFO][0m 15:28:54 kernel 执行完成 (16s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:28:54 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:28:54   找到JSON: /tmp/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:54 处理 JSON: memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:28:54   执行贪心算法...
[0;34m[INFO][0m 15:28:54     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=partial allocated=321/333 failed=12
[adas_memplan] memory-summary: original_total=113.22 MB, scaled_total=268.91 MB, local_peak=2.94 MB, weight_peak=13.07 MB
[0;32m[SUCCESS][0m 15:28:54     贪心算法成功
[0;34m[INFO][0m 15:28:54     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:28:54     贪心可视化成功
[0;34m[INFO][0m 15:28:54   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:28:54     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=333/333 failed=0
[adas_memplan_cp] memory-summary: original_total=113.22 MB, scaled_total=268.91 MB, local_peak=2.99 MB, weight_peak=13.04 MB
[0;32m[SUCCESS][0m 15:29:56     OR-Tools fast 成功
[0;34m[INFO][0m 15:29:56     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:29:56     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:29:56   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:29:56     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_flash_attention/memory_layout_debug_tmpe0hrbhxd.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/333 failed=333
[0;31m[ERROR][0m 15:29:56     OR-Tools optimal 失败
[0;34m[INFO][0m 15:29:56 进度: 5/17, 已用时: 160s, 预计剩余: 384s

[0;35m[SECTION][0m 15:29:56 [6/17] 处理 m8_14b/sp2tp8_native_attention_pipeline
=====================================
[0;34m[INFO][0m 15:29:56 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/sp2tp8_native_attention_pipeline.py
[0;34m[INFO][0m 15:29:56 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/sp2tp8_native_attention_pipeline.py
[0;34m[INFO][0m 15:29:56 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:29:56 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:29:56 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
topo:  1
short_attn_kernel
fp32[constexpr[1024], constexpr[4], constexpr[128]]

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m

compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 1104 (Bank:490 Region:614), score: 1.320423e+05
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 347 个逻辑分配, 347 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 3264 对, Region冲突 4190 对, 总计 7454 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:30:20.609838 161620 ada_task.cc:233] ###### finish task, AIU elasped time is:1797644 ns, pe start index is:0
I0810 15:30:20.839578 161620 ada_task.cc:233] ###### finish task, AIU elasped time is:1784772 ns, pe start index is:0
topo:  1
short_attn_kernel
tensor(2, dtype=torch.int8)
aot bdmodel path is: /root/.triton/cache/LZBK7PBQVAT5HVOS7AZWPB7MMGWBXUBCZLWVSQPARMH2Z2LUMAZQ/gen_bdmodel/out.bdmodel
latency(ms) is 1.784772
[0;34m[INFO][0m 15:30:21 执行完成，检查输出...
[0;34m[INFO][0m 15:30:21 kernel 执行完成 (25s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:30:21 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:30:21   找到JSON: /tmp/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:30:21 处理 JSON: memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:30:21   执行贪心算法...
[0;34m[INFO][0m 15:30:21     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=347/347 failed=0
[adas_memplan] memory-summary: original_total=81.18 MB, scaled_total=192.79 MB, local_peak=2.90 MB, weight_peak=13.36 MB
[0;32m[SUCCESS][0m 15:30:22     贪心算法成功
[0;34m[INFO][0m 15:30:22     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:30:22     贪心可视化成功
[0;34m[INFO][0m 15:30:22   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:30:22     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=347/347 failed=0
[adas_memplan_cp] memory-summary: original_total=81.18 MB, scaled_total=192.79 MB, local_peak=2.52 MB, weight_peak=13.74 MB
[0;32m[SUCCESS][0m 15:30:25     OR-Tools fast 成功
[0;34m[INFO][0m 15:30:25     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:30:25     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:30:25   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:30:25     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__sp2tp8_native_attention_pipeline/memory_layout_debug_tmpflam3_si.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/347 failed=347
[0;31m[ERROR][0m 15:30:26     OR-Tools optimal 失败
[0;34m[INFO][0m 15:30:26 进度: 6/17, 已用时: 190s, 预计剩余: 341s

[0;35m[SECTION][0m 15:30:26 [7/17] 处理 m8_14b/test_bandwidth_selfattn_sp2tp8
=====================================
[0;34m[INFO][0m 15:30:26 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/test_bandwidth_selfattn_sp2tp8.py
[0;34m[INFO][0m 15:30:26 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/test_bandwidth_selfattn_sp2tp8.py
[0;34m[INFO][0m 15:30:26 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:30:26 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:30:26 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
topo:  0
bandwidth_attn_kernel

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m

compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: Minimalloc → conflicts: 28 (Bank:9 Region:19), score: 3.819770e+03
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 37 个逻辑分配, 37 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 58 对, Region冲突 108 对, 总计 166 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:30:34.702740 163142 ada_task.cc:233] ###### finish task, AIU elasped time is:494050 ns, pe start index is:0
I0810 15:30:34.879915 163142 ada_task.cc:233] ###### finish task, AIU elasped time is:493284 ns, pe start index is:0
topo:  0
bandwidth_attn_kernel
tensor(1, dtype=torch.int8)
aot bdmodel path is: /root/.triton/cache/GXJWBZQAW5IYXDB3U4HWLK5VYVT6JH772CBUVC6FTT2D4BDOB62Q/gen_bdmodel/out.bdmodel
latency(ms) is 0.493284
[0;34m[INFO][0m 15:30:35 执行完成，检查输出...
[0;34m[INFO][0m 15:30:35 kernel 执行完成 (9s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:30:35 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:30:35   找到JSON: /tmp/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:30:35 处理 JSON: memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:30:35   执行贪心算法...
[0;34m[INFO][0m 15:30:35     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=partial allocated=36/37 failed=1
[adas_memplan] memory-summary: original_total=14.05 MB, scaled_total=33.38 MB, local_peak=2.42 MB, weight_peak=13.51 MB
[0;32m[SUCCESS][0m 15:30:35     贪心算法成功
[0;34m[INFO][0m 15:30:35     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:30:35     贪心可视化成功
[0;34m[INFO][0m 15:30:35   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:30:35     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=fail allocated=0/37 failed=37
[0;31m[ERROR][0m 15:30:36     OR-Tools fast 失败
[0;34m[INFO][0m 15:30:36   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:30:36     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_bandwidth_selfattn_sp2tp8/memory_layout_debug_tmpigqk4eas.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/37 failed=37
[0;31m[ERROR][0m 15:30:36     OR-Tools optimal 失败
[0;34m[INFO][0m 15:30:36 进度: 7/17, 已用时: 200s, 预计剩余: 280s

[0;35m[SECTION][0m 15:30:36 [8/17] 处理 m8_14b/test_short_load_store_selfattn_sp2tp8
=====================================
[0;34m[INFO][0m 15:30:36 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/test_short_load_store_selfattn_sp2tp8.py
[0;34m[INFO][0m 15:30:36 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/m8_14b/test_short_load_store_selfattn_sp2tp8.py
[0;34m[INFO][0m 15:30:36 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:30:36 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:30:36 开始执行...
[32mINFO: Initialized triton_ada_dnn v0.1.0[0m
topo:  1
short_attn_kernel
fp32[constexpr[896], constexpr[4], constexpr[128]]
fp32[constexpr[896], constexpr[4], constexpr[128]]
fp32[constexpr[896], constexpr[4], constexpr[128]]
fp32[constexpr[896], constexpr[4], constexpr[128]]

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3[36mDEBUG: Using selector: EpollSelector[0m

compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: BottleneckRelax → conflicts: 3986 (Bank:2003 Region:1983), score: 4.482398e+05
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 1186 个逻辑分配, 1186 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 11394 对, Region冲突 14634 对, 总计 26028 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:32:39.312258 163927 ada_task.cc:233] ###### finish task, AIU elasped time is:5742880 ns, pe start index is:0
I0810 15:32:39.674111 163927 ada_task.cc:233] ###### finish task, AIU elasped time is:5742948 ns, pe start index is:0
topo:  1
short_attn_kernel
tensor(2, dtype=torch.int8)
aot bdmodel path is: /root/.triton/cache/ECUX3CRQU3YJ2HQNBKKGO244T7AC4LVLGMTURXGN7OCJUM5HNX6A/gen_bdmodel/out.bdmodel
latency(ms) is 5.742948
[0;34m[INFO][0m 15:32:41 执行完成，检查输出...
[0;34m[INFO][0m 15:32:41 kernel 执行完成 (125s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:32:41 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:32:41   找到JSON: /tmp/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:32:41 处理 JSON: memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:32:41   执行贪心算法...
[0;34m[INFO][0m 15:32:41     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=partial allocated=1183/1186 failed=3
[adas_memplan] memory-summary: original_total=281.35 MB, scaled_total=668.21 MB, local_peak=2.96 MB, weight_peak=12.77 MB
[0;32m[SUCCESS][0m 15:32:42     贪心算法成功
[0;34m[INFO][0m 15:32:42     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:32:42     贪心可视化成功
[0;34m[INFO][0m 15:32:42   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:32:42     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=1186/1186 failed=0
[adas_memplan_cp] memory-summary: original_total=281.35 MB, scaled_total=668.21 MB, local_peak=2.64 MB, weight_peak=13.14 MB
[0;32m[SUCCESS][0m 15:35:43     OR-Tools fast 成功
[0;34m[INFO][0m 15:35:43     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 15:35:43     OR-Tools fast 可视化成功
[0;34m[INFO][0m 15:35:43   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:35:43     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/m8_14b__test_short_load_store_selfattn_sp2tp8/memory_layout_debug_tmp15z4e3f1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/1186 failed=1186
[0;31m[ERROR][0m 15:35:45     OR-Tools optimal 失败
[0;34m[INFO][0m 15:35:45 进度: 8/17, 已用时: 509s, 预计剩余: 567s

[0;35m[SECTION][0m 15:35:45 [9/17] 处理 x2i/x2i_atten_kernel
=====================================
[0;34m[INFO][0m 15:35:45 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_kernel.py
[0;34m[INFO][0m 15:35:45 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_kernel.py
[0;34m[INFO][0m 15:35:45 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:35:45 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:35:45 开始执行...
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_kernel.py:1436: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_kernel.py":99:46): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeRelax → conflicts: 6237 (Bank:1990 Region:4247), score: 7.109844e+05
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 2632 个逻辑分配, 2632 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 13942 对, Region冲突 28140 对, 总计 42082 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:38:55.396621 166245 ada_task.cc:233] ###### finish task, AIU elasped time is:******** ns, pe start index is:0
------ Attention Info ------
Tensor Q Shape is torch.Size([32, 8192, 128]), Dtype is torch.float16
Tensor K Shape is torch.Size([32, 8192, 128]), Dtype is torch.float16
Tensor Mask Shape is torch.Size([8192]), Dtype is torch.float16
The maximum difference between torch and triton is 0.00146484375
[0;34m[INFO][0m 15:38:55 执行完成，检查输出...
[0;34m[INFO][0m 15:38:55 kernel 执行完成 (190s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:38:55 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:38:55   找到JSON: /tmp/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:38:55 处理 JSON: memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:38:55   执行贪心算法...
[0;34m[INFO][0m 15:38:55     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=2632/2632 failed=0
[adas_memplan] memory-summary: original_total=817.81 MB, scaled_total=1942.32 MB, local_peak=2.83 MB, weight_peak=10.79 MB
[0;32m[SUCCESS][0m 15:38:56     贪心算法成功
[0;34m[INFO][0m 15:38:56     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:38:56     贪心可视化成功
[0;34m[INFO][0m 15:38:56   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:38:56     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=fail allocated=0/2632 failed=2632
[0;31m[ERROR][0m 15:52:40     OR-Tools fast 失败
[0;34m[INFO][0m 15:52:40   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 15:52:40     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_kernel/memory_layout_debug_tmpddj3sj05.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/2632 failed=2632
[0;31m[ERROR][0m 15:52:43     OR-Tools optimal 失败
[0;34m[INFO][0m 15:52:43 进度: 9/17, 已用时: 1527s, 预计剩余: 1352s

[0;35m[SECTION][0m 15:52:43 [10/17] 处理 x2i/x2i_atten_sp2_kernel
=====================================
[0;34m[INFO][0m 15:52:43 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py
[0;34m[INFO][0m 15:52:43 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py
[0;34m[INFO][0m 15:52:43 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 15:52:43 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 15:52:43 开始执行...
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py:1747: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":100:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":107:50): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)
/usr/lib/python3.9/ast.py:407: UserWarning: loc("/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2i/x2i_atten_sp2_kernel.py":757:46): The result of tl.advance is not being used. Note that tl.advance does not have any side effects. To move the block pointer, you need to assign the result of tl.advance to a variable.
  return visitor(node)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3
compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 2505 (Bank:1197 Region:1308), score: 3.039503e+05
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 2319 个逻辑分配, 2319 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 15656 对, Region冲突 30366 对, 总计 46022 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 15:55:20.901229 168467 ada_task.cc:233] ###### finish task, AIU elasped time is:8227562 ns, pe start index is:0
------ Attention Info ------
Tensor Q Shape is torch.Size([32, 4096, 128]), Dtype is torch.float16
Tensor K Shape is torch.Size([32, 8192, 128]), Dtype is torch.float16
Tensor Mask Shape is torch.Size([8192]), Dtype is torch.float16
The maximum difference between torch and triton is 0.001220703125
[0;34m[INFO][0m 15:55:21 执行完成，检查输出...
[0;34m[INFO][0m 15:55:21 kernel 执行完成 (158s)，检查JSON生成...
[0;32m[SUCCESS][0m 15:55:21 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 15:55:21   找到JSON: /tmp/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:55:21 处理 JSON: memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 15:55:21   执行贪心算法...
[0;34m[INFO][0m 15:55:21     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=2319/2319 failed=0
[adas_memplan] memory-summary: original_total=500.73 MB, scaled_total=1189.24 MB, local_peak=2.71 MB, weight_peak=10.69 MB
[0;32m[SUCCESS][0m 15:55:22     贪心算法成功
[0;34m[INFO][0m 15:55:22     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 15:55:22     贪心可视化成功
[0;34m[INFO][0m 15:55:22   执行 OR-Tools (fast)...
[0;34m[INFO][0m 15:55:22     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=fail allocated=0/2319 failed=2319
[0;31m[ERROR][0m 16:05:43     OR-Tools fast 失败
[0;34m[INFO][0m 16:05:43   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 16:05:43     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2i__x2i_atten_sp2_kernel/memory_layout_debug_tmpi3ilvs03.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/2319 failed=2319
[0;31m[ERROR][0m 16:05:46     OR-Tools optimal 失败
[0;34m[INFO][0m 16:05:46 进度: 10/17, 已用时: 2310s, 预计剩余: 1617s

[0;35m[SECTION][0m 16:05:46 [11/17] 处理 x2v/x2v_atten_gemm_ffn_txt
=====================================
[0;34m[INFO][0m 16:05:46 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_atten_gemm_ffn_txt.py
[0;34m[INFO][0m 16:05:46 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_atten_gemm_ffn_txt.py
[0;34m[INFO][0m 16:05:46 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 16:05:46 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 16:05:46 开始执行...

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:   8%|[37m▊         [0m| stage-1
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3
compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 30 (Bank:30 Region:0), score: 5.051575e+03
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 663 个逻辑分配, 663 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 2370 对, Region冲突 4078 对, 总计 6448 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 16:06:19.949709 170520 ada_task.cc:233] ###### finish task, AIU elasped time is:2492145 ns, pe start index is:0
torch_output  tensor([[-16.8125, -17.7969, -58.6250,  ...,  53.5312,  66.9375,   4.1992],
        [-61.5000, -16.8750, -28.8594,  ...,  90.2500,   6.2773,  -2.1133],
        [ 89.5625,  34.7500, -11.5625,  ...,  19.2188,  50.0000,   6.5625],
        ...,
        [ -1.9912,   3.2773, -41.6562,  ...,  19.0469,  31.0625,  -3.3672],
        [158.2500,  17.4219, -46.3438,  ...,  -2.7227,  73.9375,  12.9531],
        [-32.3438,  50.7188, -46.7500,  ...,  23.7188,  -5.2344,  12.5469]],
       dtype=torch.float16, grad_fn=<AddBackward0>)
triton_outputs  tensor([[-16.9688, -17.7969, -58.6562,  ...,  53.6875,  66.9375,   4.1914],
        [-61.7500, -16.8594, -28.8438,  ...,  90.1875,   6.2148,  -2.1094],
        [ 89.3750,  34.8125, -11.5859,  ...,  19.1406,  50.0000,   6.5586],
        ...,
        [ -2.0156,   3.2441, -41.7188,  ...,  19.1094,  31.0312,  -3.3809],
        [158.1250,  17.4219, -46.4375,  ...,  -2.8223,  73.8125,  12.9609],
        [-32.6875,  50.7500, -46.7500,  ...,  23.7656,  -5.2109,  12.5469]],
       dtype=torch.float16)
The maximum absolute difference between torch and triton is 2.5, 
The maximum relative difference between torch and triton is 0.66796875
差值的abs > 0.1 的比例为: 277015/ 1179648 = 0.23482852511935765
================================
最大绝对差值: 2.50000000
PyTorch 值: 654.00000000
Triton 值: 656.50000000
================================
最大相对差值: 0.66796875
PyTorch 值: 0.02539062
Triton 值: -0.64257812
[0;34m[INFO][0m 16:06:20 执行完成，检查输出...
[0;34m[INFO][0m 16:06:21 kernel 执行完成 (34s)，检查JSON生成...
[0;32m[SUCCESS][0m 16:06:21 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 16:06:21   找到JSON: /tmp/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:06:21 处理 JSON: memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:06:21   执行贪心算法...
[0;34m[INFO][0m 16:06:21     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=663/663 failed=0
[adas_memplan] memory-summary: original_total=190.92 MB, scaled_total=453.43 MB, local_peak=1.09 MB, weight_peak=5.69 MB
[0;32m[SUCCESS][0m 16:06:21     贪心算法成功
[0;34m[INFO][0m 16:06:21     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 16:06:21     贪心可视化成功
[0;34m[INFO][0m 16:06:21   执行 OR-Tools (fast)...
[0;34m[INFO][0m 16:06:21     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=663/663 failed=0
[adas_memplan_cp] memory-summary: original_total=190.92 MB, scaled_total=453.43 MB, local_peak=1.02 MB, weight_peak=5.70 MB
[0;32m[SUCCESS][0m 16:07:06     OR-Tools fast 成功
[0;34m[INFO][0m 16:07:06     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 16:07:06     OR-Tools fast 可视化成功
[0;34m[INFO][0m 16:07:06   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 16:07:06     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=663/663 failed=0
[adas_memplan_cp] memory-summary: original_total=190.92 MB, scaled_total=453.43 MB, local_peak=1.02 MB, weight_peak=5.70 MB
[0;32m[SUCCESS][0m 16:07:09     OR-Tools optimal 成功
[0;34m[INFO][0m 16:07:09     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_atten_gemm_ffn_txt/memory_layout_debug_tmpg02e1spb.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 16:07:09     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 16:07:09 进度: 11/17, 已用时: 2393s, 预计剩余: 1302s

[0;35m[SECTION][0m 16:07:09 [12/17] 处理 x2v/x2v_qkv_gemm_rope_txt
=====================================
[0;34m[INFO][0m 16:07:09 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_txt.py
[0;34m[INFO][0m 16:07:09 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_txt.py
[0;34m[INFO][0m 16:07:09 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 16:07:09 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 16:07:09 开始执行...
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_txt.py:548: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  33%|[37m███▎      [0m| stage-4[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 2 (Bank:2 Region:0), score: 1.644114e+03
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 51 个逻辑分配, 51 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 94 对, Region冲突 164 对, 总计 258 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 16:07:14.302384 172317 ada_task.cc:233] ###### finish task, AIU elasped time is:182480 ns, pe start index is:0
[31m
==============Comparing q_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.015625, 
The maximum relative difference between torch and triton is 0.********
差值的abs > 0.1 的比例为: 0/ 147456 = 0.0
================================
最大绝对差值: 0.********
PyTorch 值: 13.********
Triton 值: 13.17187500
================================
最大相对差值: 0.********
PyTorch 值: -0.88671875
Triton 值: -0.87500000
======================================================
[0m
[32m
==============Comparing k_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.015625, 
The maximum relative difference between torch and triton is 0.007663726806640625
差值的abs > 0.1 的比例为: 0/ 147456 = 0.0
================================
最大绝对差值: 0.********
PyTorch 值: -13.26562500
Triton 值: -13.28125000
================================
最大相对差值: 0.00766373
PyTorch 值: -1.01953125
Triton 值: -1.02734375
======================================================
[0m
[33m
==============Comparing v_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.01708984375
差值的abs > 0.1 的比例为: 0/ 147456 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: 32.84375000
Triton 值: 32.87500000
================================
最大相对差值: 0.01708984
PyTorch 值: 0.67236328
Triton 值: 0.65527344
======================================================
[0m
[0;34m[INFO][0m 16:07:16 执行完成，检查输出...
[0;34m[INFO][0m 16:07:16 kernel 执行完成 (7s)，检查JSON生成...
[0;32m[SUCCESS][0m 16:07:16 找到 1 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 16:07:16   找到JSON: /tmp/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:16 处理 JSON: memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:16   执行贪心算法...
[0;34m[INFO][0m 16:07:16     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=51/51 failed=0
[adas_memplan] memory-summary: original_total=7.50 MB, scaled_total=17.80 MB, local_peak=0.83 MB, weight_peak=5.34 MB
[0;32m[SUCCESS][0m 16:07:16     贪心算法成功
[0;34m[INFO][0m 16:07:16     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 16:07:16     贪心可视化成功
[0;34m[INFO][0m 16:07:16   执行 OR-Tools (fast)...
[0;34m[INFO][0m 16:07:16     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=51/51 failed=0
[adas_memplan_cp] memory-summary: original_total=7.50 MB, scaled_total=17.80 MB, local_peak=0.53 MB, weight_peak=5.37 MB
[0;32m[SUCCESS][0m 16:07:16     OR-Tools fast 成功
[0;34m[INFO][0m 16:07:16     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 16:07:16     OR-Tools fast 可视化成功
[0;34m[INFO][0m 16:07:16   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 16:07:16     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=51/51 failed=0
[adas_memplan_cp] memory-summary: original_total=7.50 MB, scaled_total=17.80 MB, local_peak=0.42 MB, weight_peak=5.38 MB
[0;32m[SUCCESS][0m 16:07:17     OR-Tools optimal 成功
[0;34m[INFO][0m 16:07:17     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_txt/memory_layout_debug_tmp7a01u4fm.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 16:07:17     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 16:07:17 进度: 12/17, 已用时: 2401s, 预计剩余: 1000s

[0;35m[SECTION][0m 16:07:17 [13/17] 处理 x2v/x2v_qkv_gemm_rope_v0
=====================================
[0;34m[INFO][0m 16:07:17 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_v0.py
[0;34m[INFO][0m 16:07:17 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_v0.py
[0;34m[INFO][0m 16:07:17 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 16:07:17 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 16:07:17 开始执行...
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_v0.py:554: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)
q_out  fp16[constexpr[208], constexpr[24], constexpr[128]]
q_out  fp16[constexpr[4], constexpr[52], constexpr[3072]]
k_out  fp16[constexpr[208], constexpr[24], constexpr[128]]
k_out  fp16[constexpr[4], constexpr[52], constexpr[3072]]

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3
compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeRelax → conflicts: 107 (Bank:38 Region:69), score: 1.904135e+04
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 114 个逻辑分配, 114 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 346 对, Region冲突 918 对, 总计 1264 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling: 100%|[37m██████████[0m| stage-12WARNING: Logging before InitGoogleLogging() is written to STDERR
I0810 16:07:28.445537 173333 ada_task.cc:233] ###### finish task, AIU elasped time is:2595692 ns, pe start index is:0
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_v0.py:554: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)

compiling:   0%|[37m          [0m| stage-0 
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  25%|[37m██▌       [0m| stage-3
compiling:  33%|[37m███▎      [0m| stage-4[31m
==============Comparing q_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.015625
差值的abs > 0.1 的比例为: 0/ 10223616 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: 17.73437500
Triton 值: 17.70312500
================================
最大相对差值: 0.********
PyTorch 值: 0.21093750
Triton 值: 0.22656250
======================================================
[0m
[32m
==============Comparing k_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.********
差值的abs > 0.1 的比例为: 0/ 10223616 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: -17.56250000
Triton 值: -17.59375000
================================
最大相对差值: 0.********
PyTorch 值: -0.47265625
Triton 值: -0.48437500
======================================================
[0m
[33m
==============Comparing v_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.01953125
差值的abs > 0.1 的比例为: 0/ 10223616 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: 36.12500000
Triton 值: 36.09375000
================================
最大相对差值: 0.01953125
PyTorch 值: -0.********
Triton 值: -0.********
======================================================
[0m
q_out  fp16[constexpr[24], constexpr[24], constexpr[128]]
q_out  fp16[constexpr[4], constexpr[6], constexpr[3072]]
k_out  fp16[constexpr[24], constexpr[24], constexpr[128]]
k_out  fp16[constexpr[4], constexpr[6], constexpr[3072]]

compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
[MemPlan] SELECTED: GreedyAllocEarliestEdgeStrict → conflicts: 0 (Bank:0 Region:0), score: 1.008428e+03
[内存规划可视化] 调试信息已输出到: /tmp/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json
[内存规划可视化] [AdaMemoryPlanning] 包含 114 个逻辑分配, 114 个物理偏移分配
[内存规划可视化] [AdaMemoryPlanning] 冲突统计: Bank冲突 346 对, Region冲突 914 对, 总计 1260 对
[内存规划可视化] [AdaMemoryPlanning] 检测到逻辑冲突！请查看JSON文件了解详情

compiling:  50%|[37m█████     [0m| stage-6
compiling:  58%|[37m█████▊    [0m| stage-7ada1 compilation of bdmodel will stop early. If you want to turn off this function, please enable --skip-ada1-fast-stop

compiling:  75%|[37m███████▌  [0m| stage-9
compiling:  83%|[37m████████▎ [0m| stage-10
compiling: 100%|[37m██████████[0m| stage-12I0810 16:07:35.978621 173333 ada_task.cc:233] ###### finish task, AIU elasped time is:704897 ns, pe start index is:0
[31m
==============Comparing q_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.015625, 
The maximum relative difference between torch and triton is 0.********
差值的abs > 0.1 的比例为: 0/ 1179648 = 0.0
================================
最大绝对差值: 0.********
PyTorch 值: -9.56250000
Triton 值: -9.57812500
================================
最大相对差值: 0.********
PyTorch 值: -0.19921875
Triton 值: -0.21093750
======================================================
[0m
[32m
==============Comparing k_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.********
差值的abs > 0.1 的比例为: 0/ 1179648 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: -17.12500000
Triton 值: -17.09375000
================================
最大相对差值: 0.********
PyTorch 值: 0.58593750
Triton 值: 0.59765625
======================================================
[0m
[33m
==============Comparing v_out between torch and triton==================
The maximum absolute difference between torch and triton is 0.03125, 
The maximum relative difference between torch and triton is 0.01806640625
差值的abs > 0.1 的比例为: 0/ 1179648 = 0.0
================================
最大绝对差值: 0.03125000
PyTorch 值: -32.28125000
Triton 值: -32.25000000
================================
最大相对差值: 0.01806641
PyTorch 值: 0.64453125
Triton 值: 0.66259766
======================================================
[0m
[0;34m[INFO][0m 16:07:37 执行完成，检查输出...
[0;34m[INFO][0m 16:07:37 kernel 执行完成 (20s)，检查JSON生成...
[0;32m[SUCCESS][0m 16:07:37 找到 2 个内存规划 JSON 文件 - 视为成功
[0;34m[INFO][0m 16:07:37   找到JSON: /tmp/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:37   找到JSON: /tmp/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:37 处理 JSON: memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:37   执行贪心算法...
[0;34m[INFO][0m 16:07:37     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=fail allocated=94/114 failed=20
[adas_memplan] memory-summary: original_total=78.76 MB, scaled_total=187.06 MB, local_peak=2.99 MB, weight_peak=13.46 MB
[0;31m[ERROR][0m 16:07:37     贪心算法失败
[0;34m[INFO][0m 16:07:37   执行 OR-Tools (fast)...
[0;34m[INFO][0m 16:07:37     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=114/114 failed=0
[adas_memplan_cp] memory-summary: original_total=78.76 MB, scaled_total=187.06 MB, local_peak=2.89 MB, weight_peak=13.51 MB
[0;32m[SUCCESS][0m 16:07:38     OR-Tools fast 成功
[0;34m[INFO][0m 16:07:38     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 16:07:38     OR-Tools fast 可视化成功
[0;34m[INFO][0m 16:07:38   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 16:07:38     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmpkspx2f13.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=fail allocated=0/114 failed=114
[0;31m[ERROR][0m 16:07:39     OR-Tools optimal 失败
[0;34m[INFO][0m 16:07:39 处理 JSON: memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json
[0;34m[INFO][0m 16:07:39   执行贪心算法...
[0;34m[INFO][0m 16:07:39     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan.py" --input "/tmp/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --chronological
[adas_memplan] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json
[adas_memplan] status=success allocated=114/114 failed=0
[adas_memplan] memory-summary: original_total=57.02 MB, scaled_total=135.41 MB, local_peak=2.01 MB, weight_peak=8.92 MB
[0;32m[SUCCESS][0m 16:07:39     贪心算法成功
[0;34m[INFO][0m 16:07:39     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/greedy.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/greedy.html
[0;32m[SUCCESS][0m 16:07:39     贪心可视化成功
[0;34m[INFO][0m 16:07:39   执行 OR-Tools (fast)...
[0;34m[INFO][0m 16:07:39     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "60" --workers "0" --mode fast --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json
[adas_memplan_cp] status=success allocated=114/114 failed=0
[adas_memplan_cp] memory-summary: original_total=57.02 MB, scaled_total=135.41 MB, local_peak=1.00 MB, weight_peak=9.07 MB
[0;32m[SUCCESS][0m 16:07:40     OR-Tools fast 成功
[0;34m[INFO][0m 16:07:40     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_fast.html
[0;32m[SUCCESS][0m 16:07:40     OR-Tools fast 可视化成功
[0;34m[INFO][0m 16:07:40   执行 OR-Tools (optimal)...
[0;34m[INFO][0m 16:07:40     CMD: python3 "/workspace/toolchain/tools/adas_memplan/adas_memplan_cp_sat.py" --input "/tmp/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --local-cap "3145728" --weight-cap "16777216" --granularity "64" --local-scale 2.375 --weight-scale 2.375 --max-solve-seconds "300" --workers "0" --mode optimal --objective cost
[adas_memplan_cp] result written to: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json
[adas_memplan_cp] status=success allocated=114/114 failed=0
[adas_memplan_cp] memory-summary: original_total=57.02 MB, scaled_total=135.41 MB, local_peak=1.00 MB, weight_peak=9.07 MB
[0;32m[SUCCESS][0m 16:07:40     OR-Tools optimal 成功
[0;34m[INFO][0m 16:07:40     VIZ: python3 "/workspace/toolchain/tools/adas_memplan/adas_memory_visualizer_adas_dual.py" --input "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.json" --output "/workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html"
[adas_dual_viz] written: /workspace/toolchain/work/adas_batch/x2v__x2v_qkv_gemm_rope_v0/memory_layout_debug_tmplog4wom1.mvp.mlir.mid_AdaMemoryPlanning/cp_optimal.html
[0;32m[SUCCESS][0m 16:07:40     OR-Tools optimal 可视化成功
[0;34m[INFO][0m 16:07:40 进度: 13/17, 已用时: 2424s, 预计剩余: 744s

[0;35m[SECTION][0m 16:07:40 [14/17] 处理 x2v/x2v_qkv_gemm_rope_vid_v1
=====================================
[0;34m[INFO][0m 16:07:40 执行 kernel: /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_vid_v1.py
[0;34m[INFO][0m 16:07:40 执行命令: cd /workspace/work/triton-ada-dnn && python3 /workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_vid_v1.py
[0;34m[INFO][0m 16:07:40 工作目录: /workspace/work/triton-ada-dnn
[0;34m[INFO][0m 16:07:40 Python路径: /workspace/work/triton-ada-dnn:/workspace/work/triton-ada-dnn:/workspace::/workspace/toolchain/bdgir/python:/workspace/toolchain/build/compiler/python_packages:/workspace/toolchain/build/lib
[0;34m[INFO][0m 16:07:40 开始执行...
/workspace/work/triton-ada-dnn/triton_ada_dnn/models/ada1/x2v/x2v_qkv_gemm_rope_vid_v1.py:727: UserWarning: Set seed for `ada` device does not take effect, please add API's `_is_in_bad_fork` and `manual_seed_all` to `ada` device module.
  torch.manual_seed(42)

compiling:   0%|[37m          [0m| stage-0
compiling:   0%|[37m          [0m| stage-0
compiling:  17%|[37m█▋        [0m| stage-2
compiling:  33%|[37m███▎      [0m| stage-4
compiling:  42%|[37m████▏     [0m| stage-5[MemPlan] Using default strategy sequence
