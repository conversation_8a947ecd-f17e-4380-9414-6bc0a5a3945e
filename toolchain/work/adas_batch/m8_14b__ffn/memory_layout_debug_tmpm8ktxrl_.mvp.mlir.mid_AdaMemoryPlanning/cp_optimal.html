<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>AdaS SRAM Allocation (Dual Canvas)</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin:0; background:#f5f5f5; }
    .header { padding: 16px 20px; background: white; border-bottom: 1px solid #e1e4e8; }
    .header h1 { margin:0; font-size: 20px; color:#333; }
    .main { display:flex; gap: 0; }
    .left { flex: 1; padding: 16px; }
    .panel { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; margin-bottom:16px; }
    .row { display:flex; gap:12px; align-items:center; flex-wrap:wrap; }
    .stat { display:flex; gap:16px; }
    .stat .box { background:#f8f9fa; padding:8px 10px; border-radius:4px; border-left:3px solid #4CAF50; }
    .canvasWrap { background:white; border:1px solid #e1e4e8; border-radius:4px; padding:8px; margin-bottom:12px; }
    .canvasHeader { display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; color:#333; }
    .legend { display:flex; gap:16px; }
    .legend .item { display:flex; gap:6px; align-items:center; }
    .colorBox { width:14px; height:10px; border-radius:2px; }
    .detail { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; }
    canvas { width: 100%; height: 260px; display:block; background:#fff; }
    .note { color:#666; font-size:12px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>AdaS SRAM Memory Allocation (Dual Canvas)</h1>
    <div class="note">双画布同步：上=Local (3MB)，下=Weight (16MB)。左右滚动/缩放同步；点击条块显示详情。</div>
  </div>
  <div class="main">
    <div class="left" style="width:100%">
      <div class="panel">
        <div class="row">
          <label>View Range:</label>
          <input type="range" id="viewRange" min="100" max="5000" value="1000" step="100"/>
          <span id="viewRangeValue">1000</span>
          <label>View Offset:</label>
          <input type="range" id="viewOffset" min="0" max="10000" value="0" step="10"/>
          <span id="viewOffsetValue">0</span>
          <button id="resetBtn">Reset</button>
          <div class="legend">
            <div class="item"><div class="colorBox" style="background:#2196F3"></div>Normal</div>
            <div class="item"><div class="colorBox" style="background:#FFD700"></div>Pref Violation</div>
            <div class="item"><div class="colorBox" style="background:#FF5722"></div>Explicit</div>
          </div>
        </div>
        <div class="stat" style="margin-top:8px">
          <div class="box">Total: <b id="statTotal">0</b></div>
          <div class="box">Local Peak: <b id="statLocalPeak">0MB</b></div>
          <div class="box">Weight Peak: <b id="statWeightPeak">0MB</b></div>
          <div class="box">Pref Violations: <b id="statPV">0</b></div>
        </div>
      </div>

      <div class="canvasWrap">
        <div class="canvasHeader"><b>Local Pool (3MB)</b><span class="note">Y: 0..3MB</span></div>
        <canvas id="canvasLocal"></canvas>
      </div>
      <div class="canvasWrap">
        <div class="canvasHeader"><b>Weight Pool (16MB)</b><span class="note">Y: 0..16MB</span></div>
        <canvas id="canvasWeight"></canvas>
      </div>
      <div class="detail" id="detailPanel">点击条块查看详细信息</div>
    </div>
  </div>

  <script>
    const data = {"status": "success", "config": {"local_cap": 3145728, "weight_cap": 16777216, "weight_base": 4194304, "local_scale": 2.375, "weight_scale": 2.375, "granularity": 64, "scale_explicit": "keep", "max_solve_seconds": 300, "workers": 0, "mode": "optimal", "objective": "cost"}, "stats": {"total_tasks": 32, "allocated": 32, "failed": 0, "preference_violation_cost": 0, "preference_violation_bytes": 0, "preference_violation_count": 0}, "allocations": [{"id": 0, "name": "%alloc_120", "alias_id": 0, "names": ["%alloc_120"], "bytes_scaled": 960, "size": 960, "t0": 709, "t1": 720, "start_time": 709, "end_time": 720, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 1, "name": "%alloc_71", "alias_id": 1, "names": ["%alloc_71"], "bytes_scaled": 2495232, "size": 2495232, "t0": 347, "t1": 382, "start_time": 347, "end_time": 382, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 2, "name": "%alloc_101", "alias_id": 2, "names": ["%alloc_101"], "bytes_scaled": 1566208, "size": 1566208, "t0": 543, "t1": 552, "start_time": 543, "end_time": 552, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 3, "name": "%alloc_47", "alias_id": 3, "names": ["%alloc_47"], "bytes_scaled": 2495232, "size": 2495232, "t0": 237, "t1": 262, "start_time": 237, "end_time": 262, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 4, "name": "%alloc_62", "alias_id": 4, "names": ["%alloc_62"], "bytes_scaled": 2495232, "size": 2495232, "t0": 305, "t1": 332, "start_time": 305, "end_time": 332, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 5, "name": "%alloc_45", "alias_id": 5, "names": ["%alloc_45"], "bytes_scaled": 19776, "size": 19776, "t0": 229, "t1": 450, "start_time": 229, "end_time": 450, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2495232, "violated_preference": false}, {"id": 6, "name": "%alloc_86", "alias_id": 6, "names": ["%alloc_86"], "bytes_scaled": 1566208, "size": 1566208, "t0": 473, "t1": 500, "start_time": 473, "end_time": 500, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 7, "name": "%alloc_80", "alias_id": 7, "names": ["%alloc_80"], "bytes_scaled": 19776, "size": 19776, "t0": 445, "t1": 612, "start_time": 445, "end_time": 612, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1566208, "violated_preference": false}, {"id": 8, "name": "%alloc_92", "alias_id": 8, "names": ["%alloc_92"], "bytes_scaled": 1566208, "size": 1566208, "t0": 501, "t1": 528, "start_time": 501, "end_time": 528, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 9, "name": "%alloc_98", "alias_id": 9, "names": ["%alloc_98"], "bytes_scaled": 1566208, "size": 1566208, "t0": 529, "t1": 552, "start_time": 529, "end_time": 552, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1585984, "violated_preference": false}, {"id": 10, "name": "%alloc_59", "alias_id": 10, "names": ["%alloc_59"], "bytes_scaled": 2495232, "size": 2495232, "t0": 291, "t1": 318, "start_time": 291, "end_time": 318, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2515008, "violated_preference": false}, {"id": 11, "name": "%alloc_46", "alias_id": 11, "names": ["%alloc_46"], "bytes_scaled": 162368, "size": 162368, "t0": 173, "t1": 450, "start_time": 173, "end_time": 450, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 5010240, "violated_preference": false}, {"id": 12, "name": "%alloc_12", "alias_id": 12, "names": ["%alloc_12"], "bytes_scaled": 389120, "size": 389120, "t0": 255, "t1": 450, "start_time": 255, "end_time": 450, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 13, "name": "%alloc", "alias_id": 13, "names": ["%alloc"], "bytes_scaled": 75712, "size": 75712, "t0": 5, "t1": 242, "start_time": 5, "end_time": 242, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2515008, "violated_preference": false}, {"id": 14, "name": "%alloc_0", "alias_id": 14, "names": ["%alloc_0"], "bytes_scaled": 661504, "size": 661504, "t0": 605, "t1": 708, "start_time": 605, "end_time": 708, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 15, "name": "%alloc_3", "alias_id": 15, "names": ["%alloc_3"], "bytes_scaled": 1245184, "size": 1245184, "t0": 451, "t1": 612, "start_time": 451, "end_time": 612, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 16, "name": "%alloc_74", "alias_id": 16, "names": ["%alloc_74"], "bytes_scaled": 2495232, "size": 2495232, "t0": 361, "t1": 382, "start_time": 361, "end_time": 382, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2515008, "violated_preference": false}, {"id": 17, "name": "%alloc_77", "alias_id": 17, "names": ["%alloc_77"], "bytes_scaled": 1566208, "size": 1566208, "t0": 373, "t1": 472, "start_time": 373, "end_time": 472, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 5172608, "violated_preference": false}, {"id": 18, "name": "%alloc_89", "alias_id": 18, "names": ["%alloc_89"], "bytes_scaled": 1566208, "size": 1566208, "t0": 487, "t1": 514, "start_time": 487, "end_time": 514, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1585984, "violated_preference": false}, {"id": 19, "name": "%alloc_83", "alias_id": 19, "names": ["%alloc_83"], "bytes_scaled": 1566208, "size": 1566208, "t0": 459, "t1": 486, "start_time": 459, "end_time": 486, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1585984, "violated_preference": false}, {"id": 20, "name": "%alloc_56", "alias_id": 20, "names": ["%alloc_56"], "bytes_scaled": 2495232, "size": 2495232, "t0": 277, "t1": 304, "start_time": 277, "end_time": 304, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 21, "name": "%alloc_95", "alias_id": 21, "names": ["%alloc_95"], "bytes_scaled": 1566208, "size": 1566208, "t0": 515, "t1": 542, "start_time": 515, "end_time": 542, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3152192, "violated_preference": false}, {"id": 22, "name": "%alloc_79", "alias_id": 22, "names": ["%alloc_79"], "bytes_scaled": 194560, "size": 194560, "t0": 445, "t1": 552, "start_time": 445, "end_time": 552, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 1245184, "violated_preference": false}, {"id": 23, "name": "%alloc_14", "alias_id": 23, "names": ["%alloc_14"], "bytes_scaled": 389120, "size": 389120, "t0": 243, "t1": 450, "start_time": 243, "end_time": 450, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 389120, "violated_preference": false}, {"id": 24, "name": "%alloc_50", "alias_id": 24, "names": ["%alloc_50"], "bytes_scaled": 2495232, "size": 2495232, "t0": 249, "t1": 276, "start_time": 249, "end_time": 276, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2515008, "violated_preference": false}, {"id": 25, "name": "%alloc_24", "alias_id": 25, "names": ["%alloc_24"], "bytes_scaled": 642048, "size": 642048, "t0": 65, "t1": 242, "start_time": 65, "end_time": 242, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2590720, "violated_preference": false}, {"id": 26, "name": "%alloc_53", "alias_id": 26, "names": ["%alloc_53"], "bytes_scaled": 2495232, "size": 2495232, "t0": 263, "t1": 290, "start_time": 263, "end_time": 290, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 5172608, "violated_preference": false}, {"id": 27, "name": "%alloc_65", "alias_id": 27, "names": ["%alloc_65"], "bytes_scaled": 2495232, "size": 2495232, "t0": 319, "t1": 346, "start_time": 319, "end_time": 346, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2515008, "violated_preference": false}, {"id": 28, "name": "%alloc_81", "alias_id": 28, "names": ["%alloc_81"], "bytes_scaled": 77824, "size": 77824, "t0": 383, "t1": 612, "start_time": 383, "end_time": 612, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4718400, "violated_preference": false}, {"id": 29, "name": "%alloc_43", "alias_id": 29, "names": ["%alloc_43"], "bytes_scaled": 39232, "size": 39232, "t0": 163, "t1": 242, "start_time": 163, "end_time": 242, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3232768, "violated_preference": false}, {"id": 30, "name": "%alloc_68", "alias_id": 30, "names": ["%alloc_68"], "bytes_scaled": 2495232, "size": 2495232, "t0": 333, "t1": 360, "start_time": 333, "end_time": 360, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 5172608, "violated_preference": false}, {"id": 31, "name": "%alloc_44", "alias_id": 31, "names": ["%alloc_44"], "bytes_scaled": 622592, "size": 622592, "t0": 229, "t1": 382, "start_time": 229, "end_time": 382, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 778240, "violated_preference": false}], "failures": []};
    const localCap = data.config ? data.config.local_cap : (3*1024*1024);
    const weightCap = data.config ? data.config.weight_cap : (16*1024*1024);
    const LOCAL_MB = (localCap/(1024*1024)).toFixed(0);
    const WEIGHT_MB = (weightCap/(1024*1024)).toFixed(0);

    // State
    let viewRange = 1000;
    let viewOffset = 0;

    // Build pool-specific arrays
    const allocs = (data.allocations || []).map(a => ({
      name: a.name || (a.names && a.names[0]) || ('alloc_'+a.alias_id),
      t0: a.start_time ?? a.t0, t1: a.end_time ?? a.t1,
      size: a.size ?? a.bytes_scaled ?? 0,
      pool: a.pool || 'weight', offset: a.offset || 0,
      violated: !!a.violated_preference, is_explicit: !!a.is_explicit
    }));
    const allocsLocal = allocs.filter(a => a.pool === 'local');
    const allocsWeight = allocs.filter(a => a.pool === 'weight');

    // Elements
    const canvasLocal = document.getElementById('canvasLocal');
    const canvasWeight = document.getElementById('canvasWeight');
    const ctxLocal = canvasLocal.getContext('2d');
    const ctxWeight = canvasWeight.getContext('2d');
    const detailPanel = document.getElementById('detailPanel');

    // Resize canvases based on CSS
    function setupCanvas(canvas) {
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      const w = Math.max(800, rect.width);
      const h = 260;
      canvas.width = w * dpr; canvas.height = h * dpr;
      canvas.style.width = w + 'px'; canvas.style.height = h + 'px';
      const ctx = canvas.getContext('2d');
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    function timeToX(t, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerW = rect.width - 80;
      return 60 + ( (t - viewOffset) / viewRange ) * innerW;
    }
    function addrToY(addr, cap, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerH = rect.height - 60;
      return rect.height - 30 - (addr / cap) * innerH;
    }

    function drawGrid(ctx, canvas, capMB) {
      const rect = canvas.getBoundingClientRect();
      ctx.clearRect(0,0,rect.width,rect.height);
      ctx.fillStyle = '#fff'; ctx.fillRect(0,0,rect.width,rect.height);
      ctx.strokeStyle = '#e0e0e0'; ctx.lineWidth = 0.5; ctx.setLineDash([2,2]);
      const innerW = rect.width - 80;
      const innerH = rect.height - 60;
      // vertical time grid (10 ticks)
      const step = Math.max(1, Math.floor(viewRange/10));
      for (let t = Math.ceil(viewOffset/step)*step; t <= viewOffset+viewRange; t+=step) {
        const x = 60 + ( (t - viewOffset) / viewRange ) * innerW;
        ctx.beginPath(); ctx.moveTo(x, 20); ctx.lineTo(x, rect.height-20); ctx.stroke();
        ctx.fillStyle = '#666'; ctx.font = '11px Arial'; ctx.textAlign = 'center';
        ctx.fillText(t.toString(), x, rect.height-5);
      }
      // horizontal addr grid: every 1MB
      ctx.setLineDash([1,0]);
      ctx.strokeStyle = '#bdbdbd'; ctx.lineWidth = 1;
      for (let mb=1; mb<=capMB; ++mb) {
        const y = addrToY(mb*1024*1024, capMB*1024*1024, canvas);
        ctx.beginPath(); ctx.moveTo(60, y); ctx.lineTo(rect.width-20, y); ctx.stroke();
      }
      // axes
      ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.setLineDash([]);
      ctx.beginPath(); ctx.moveTo(60, 20); ctx.lineTo(60, rect.height-20); ctx.stroke();
      ctx.beginPath(); ctx.moveTo(60, rect.height-20); ctx.lineTo(rect.width-20, rect.height-20); ctx.stroke();
    }

    function drawPool(ctx, canvas, capBytes, items) {
      drawGrid(ctx, canvas, (capBytes/1024/1024)|0);
      const rect = canvas.getBoundingClientRect();
      items.forEach(a => {
        // skip if out of range
        if (a.t1 < viewOffset || a.t0 > viewOffset+viewRange) return;
        const x1 = Math.max(60, timeToX(a.t0, canvas));
        const x2 = Math.min(rect.width-20, timeToX(a.t1, canvas));
        if (x2 <= x1) return;
        const y = addrToY(a.offset + a.size, capBytes, canvas);
        const h = (a.size / capBytes) * (rect.height - 60);
        const fill = a.is_explicit ? '#FF5722' : (a.violated ? '#FFD700' : '#2196F3');
        ctx.fillStyle = fill;
        ctx.fillRect(x1, y, x2-x1, h);
        ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.strokeRect(x1, y, x2-x1, h);
        // store bounds for hit test
        a._bounds = {x:x1, y:y, w:(x2-x1), h:h, canvas:canvas};
      });
    }

    function renderAll() {
      setupCanvas(canvasLocal); setupCanvas(canvasWeight);
      drawPool(ctxLocal, canvasLocal, localCap, allocsLocal);
      drawPool(ctxWeight, canvasWeight, weightCap, allocsWeight);
      // stats
      document.getElementById('statTotal').textContent = allocs.length.toString();
      document.getElementById('statPV').textContent = allocs.filter(a=>a.violated).length.toString();
      const peakLocal = peakUsage(allocsLocal);
      const peakWeight = peakUsage(allocsWeight);
      document.getElementById('statLocalPeak').textContent = (peakLocal/1024/1024).toFixed(2)+ 'MB';
      document.getElementById('statWeightPeak').textContent = (peakWeight/1024/1024).toFixed(2)+ 'MB';
    }

    function peakUsage(items){
      const ev=[]; items.forEach(a=>{ ev.push({t:a.t0, d:+a.size}); ev.push({t:a.t1, d:-a.size}); });
      ev.sort((x,y)=>x.t-y.t); let cur=0, peak=0; ev.forEach(e=>{cur+=e.d; if(cur>peak) peak=cur;}); return peak;
    }

    function hitTest(canvas, x, y) {
      const list = (canvas===canvasLocal) ? allocsLocal : allocsWeight;
      for (let a of list) {
        if (!a._bounds) continue;
        const b=a._bounds; if (x>=b.x && x<=b.x+b.w && y>=b.y && y<=b.y+b.h) return a;
      }
      return null;
    }

    function showDetail(a){
      const mb=(a.size/1024/1024).toFixed(3);
      detailPanel.innerHTML = `
        <div><b>${a.name}</b></div>
        <div>Pool: ${a.pool}</div>
        <div>Time: [${a.t0} - ${a.t1}]</div>
        <div>Offset: ${a.offset} (bytes)</div>
        <div>Size: ${mb} MB</div>
        <div>Explicit: ${a.is_explicit}</div>
        <div>Pref. Violated: ${a.violated}</div>
      `;
    }

    function onCanvasClick(ev, canvas){
      const rect = canvas.getBoundingClientRect();
      const x = ev.clientX - rect.left; const y = ev.clientY - rect.top;
      const a = hitTest(canvas, x, y); if (a) showDetail(a);
    }

    // UI bindings
    document.getElementById('viewRange').addEventListener('input', e=>{
      viewRange = parseInt(e.target.value); document.getElementById('viewRangeValue').textContent=viewRange; renderAll();
    });
    document.getElementById('viewOffset').addEventListener('input', e=>{
      viewOffset = parseInt(e.target.value); document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    document.getElementById('resetBtn').addEventListener('click', ()=>{
      viewRange=1000; viewOffset=0; document.getElementById('viewRange').value=viewRange; document.getElementById('viewRangeValue').textContent=viewRange; document.getElementById('viewOffset').value=viewOffset; document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    canvasLocal.addEventListener('click', ev=> onCanvasClick(ev, canvasLocal));
    canvasWeight.addEventListener('click', ev=> onCanvasClick(ev, canvasWeight));

    // Initial
    window.addEventListener('resize', renderAll);
    renderAll();
  </script>
</body>
</html>
