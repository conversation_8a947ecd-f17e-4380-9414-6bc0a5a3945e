<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>AdaS SRAM Allocation (Dual Canvas)</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin:0; background:#f5f5f5; }
    .header { padding: 16px 20px; background: white; border-bottom: 1px solid #e1e4e8; }
    .header h1 { margin:0; font-size: 20px; color:#333; }
    .main { display:flex; gap: 0; }
    .left { flex: 1; padding: 16px; }
    .panel { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; margin-bottom:16px; }
    .row { display:flex; gap:12px; align-items:center; flex-wrap:wrap; }
    .stat { display:flex; gap:16px; }
    .stat .box { background:#f8f9fa; padding:8px 10px; border-radius:4px; border-left:3px solid #4CAF50; }
    .canvasWrap { background:white; border:1px solid #e1e4e8; border-radius:4px; padding:8px; margin-bottom:12px; }
    .canvasHeader { display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; color:#333; }
    .legend { display:flex; gap:16px; }
    .legend .item { display:flex; gap:6px; align-items:center; }
    .colorBox { width:14px; height:10px; border-radius:2px; }
    .detail { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; }
    canvas { width: 100%; height: 260px; display:block; background:#fff; }
    .note { color:#666; font-size:12px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>AdaS SRAM Memory Allocation (Dual Canvas)</h1>
    <div class="note">双画布同步：上=Local (3MB)，下=Weight (16MB)。左右滚动/缩放同步；点击条块显示详情。</div>
  </div>
  <div class="main">
    <div class="left" style="width:100%">
      <div class="panel">
        <div class="row">
          <label>View Range:</label>
          <input type="range" id="viewRange" min="100" max="5000" value="1000" step="100"/>
          <span id="viewRangeValue">1000</span>
          <label>View Offset:</label>
          <input type="range" id="viewOffset" min="0" max="10000" value="0" step="10"/>
          <span id="viewOffsetValue">0</span>
          <button id="resetBtn">Reset</button>
          <div class="legend">
            <div class="item"><div class="colorBox" style="background:#2196F3"></div>Normal</div>
            <div class="item"><div class="colorBox" style="background:#FFD700"></div>Pref Violation</div>
            <div class="item"><div class="colorBox" style="background:#FF5722"></div>Explicit</div>
          </div>
        </div>
        <div class="stat" style="margin-top:8px">
          <div class="box">Total: <b id="statTotal">0</b></div>
          <div class="box">Local Peak: <b id="statLocalPeak">0MB</b></div>
          <div class="box">Weight Peak: <b id="statWeightPeak">0MB</b></div>
          <div class="box">Pref Violations: <b id="statPV">0</b></div>
        </div>
      </div>

      <div class="canvasWrap">
        <div class="canvasHeader"><b>Local Pool (3MB)</b><span class="note">Y: 0..3MB</span></div>
        <canvas id="canvasLocal"></canvas>
      </div>
      <div class="canvasWrap">
        <div class="canvasHeader"><b>Weight Pool (16MB)</b><span class="note">Y: 0..16MB</span></div>
        <canvas id="canvasWeight"></canvas>
      </div>
      <div class="detail" id="detailPanel">点击条块查看详细信息</div>
    </div>
  </div>

  <script>
    const data = {"status": "success", "config": {"local_cap": 3145728, "weight_cap": 16777216, "weight_base": 4194304, "local_scale": 2.375, "weight_scale": 2.375, "granularity": 64, "scale_explicit": "keep", "max_solve_seconds": 300, "workers": 0, "mode": "optimal", "objective": "cost"}, "stats": {"total_tasks": 196, "allocated": 196, "failed": 0, "preference_violation_cost": 0, "preference_violation_bytes": 0, "preference_violation_count": 0}, "allocations": [{"id": 0, "name": "%alloc_368", "alias_id": 0, "names": ["%alloc_368"], "bytes_scaled": 640, "size": 640, "t0": 4221, "t1": 5072, "start_time": 4221, "end_time": 5072, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 1, "name": "%alloc_449", "alias_id": 1, "names": ["%alloc_449"], "bytes_scaled": 38912, "size": 38912, "t0": 5771, "t1": 5918, "start_time": 5771, "end_time": 5918, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 2, "name": "%alloc_341", "alias_id": 2, "names": ["%alloc_341"], "bytes_scaled": 38912, "size": 38912, "t0": 3801, "t1": 3948, "start_time": 3801, "end_time": 3948, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 3, "name": "%alloc_457", "alias_id": 3, "names": ["%alloc_457"], "bytes_scaled": 38912, "size": 38912, "t0": 5907, "t1": 6054, "start_time": 5907, "end_time": 6054, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 4, "name": "%alloc_441", "alias_id": 4, "names": ["%alloc_441"], "bytes_scaled": 38912, "size": 38912, "t0": 5635, "t1": 5782, "start_time": 5635, "end_time": 5782, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 5, "name": "%alloc_250", "alias_id": 5, "names": ["%alloc_250"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2303, "t1": 2444, "start_time": 2303, "end_time": 2444, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 6, "name": "%alloc_361", "alias_id": 6, "names": ["%alloc_361"], "bytes_scaled": 38912, "size": 38912, "t0": 4141, "t1": 4452, "start_time": 4141, "end_time": 4452, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 7, "name": "%alloc_206", "alias_id": 7, "names": ["%alloc_206"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1585, "t1": 1726, "start_time": 1585, "end_time": 1726, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 8, "name": "%alloc_249", "alias_id": 8, "names": ["%alloc_249"], "bytes_scaled": 38912, "size": 38912, "t0": 2297, "t1": 2444, "start_time": 2297, "end_time": 2444, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 9, "name": "%alloc_426", "alias_id": 9, "names": ["%alloc_426"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5339, "t1": 5480, "start_time": 5339, "end_time": 5480, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 10, "name": "%alloc_322", "alias_id": 10, "names": ["%alloc_322"], "bytes_scaled": 640, "size": 640, "t0": 3497, "t1": 4340, "start_time": 3497, "end_time": 4340, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 640, "violated_preference": false}, {"id": 11, "name": "%alloc_386", "alias_id": 11, "names": ["%alloc_386"], "bytes_scaled": 38912, "size": 38912, "t0": 4577, "t1": 4724, "start_time": 4577, "end_time": 4724, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 12, "name": "%alloc_226", "alias_id": 12, "names": ["%alloc_226"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1925, "t1": 2240, "start_time": 1925, "end_time": 2240, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 13, "name": "%alloc_418", "alias_id": 13, "names": ["%alloc_418"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5203, "t1": 5344, "start_time": 5203, "end_time": 5344, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 14, "name": "%alloc_261", "alias_id": 14, "names": ["%alloc_261"], "bytes_scaled": 38912, "size": 38912, "t0": 2501, "t1": 2648, "start_time": 2501, "end_time": 2648, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 15, "name": "%alloc_445", "alias_id": 15, "names": ["%alloc_445"], "bytes_scaled": 38912, "size": 38912, "t0": 5703, "t1": 5850, "start_time": 5703, "end_time": 5850, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 16, "name": "%alloc_310", "alias_id": 16, "names": ["%alloc_310"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3293, "t1": 3426, "start_time": 3293, "end_time": 3426, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 17, "name": "%alloc_125", "alias_id": 17, "names": ["%alloc_125"], "bytes_scaled": 38912, "size": 38912, "t0": 495, "t1": 708, "start_time": 495, "end_time": 708, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 18, "name": "%alloc_144", "alias_id": 18, "names": ["%alloc_144"], "bytes_scaled": 640, "size": 640, "t0": 669, "t1": 1478, "start_time": 669, "end_time": 1478, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 19, "name": "%alloc_143", "alias_id": 19, "names": ["%alloc_143"], "bytes_scaled": 38912, "size": 38912, "t0": 661, "t1": 1478, "start_time": 661, "end_time": 1478, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 20, "name": "%alloc_446", "alias_id": 20, "names": ["%alloc_446"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5709, "t1": 5850, "start_time": 5709, "end_time": 5850, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 21, "name": "%alloc_266", "alias_id": 21, "names": ["%alloc_266"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2575, "t1": 2708, "start_time": 2575, "end_time": 2708, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 22, "name": "%alloc_101", "alias_id": 22, "names": ["%alloc_101"], "bytes_scaled": 38912, "size": 38912, "t0": 281, "t1": 372, "start_time": 281, "end_time": 372, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 23, "name": "%alloc_374", "alias_id": 23, "names": ["%alloc_374"], "bytes_scaled": 38912, "size": 38912, "t0": 4373, "t1": 4520, "start_time": 4373, "end_time": 4520, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 24, "name": "%alloc_157", "alias_id": 24, "names": ["%alloc_157"], "bytes_scaled": 38912, "size": 38912, "t0": 845, "t1": 940, "start_time": 845, "end_time": 940, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 25, "name": "%alloc_126", "alias_id": 25, "names": ["%alloc_126"], "bytes_scaled": 640, "size": 640, "t0": 503, "t1": 708, "start_time": 503, "end_time": 708, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 640, "violated_preference": false}, {"id": 26, "name": "%alloc_434", "alias_id": 26, "names": ["%alloc_434"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5475, "t1": 5646, "start_time": 5475, "end_time": 5646, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 27, "name": "%alloc_158", "alias_id": 27, "names": ["%alloc_158"], "bytes_scaled": 1247616, "size": 1247616, "t0": 853, "t1": 940, "start_time": 853, "end_time": 940, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 640, "violated_preference": false}, {"id": 28, "name": "%alloc_394", "alias_id": 28, "names": ["%alloc_394"], "bytes_scaled": 38912, "size": 38912, "t0": 4713, "t1": 4852, "start_time": 4713, "end_time": 4852, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 29, "name": "%alloc_110", "alias_id": 29, "names": ["%alloc_110"], "bytes_scaled": 640, "size": 640, "t0": 351, "t1": 5028, "start_time": 351, "end_time": 5028, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248256, "violated_preference": false}, {"id": 30, "name": "%alloc_213", "alias_id": 30, "names": ["%alloc_213"], "bytes_scaled": 38912, "size": 38912, "t0": 1715, "t1": 1862, "start_time": 1715, "end_time": 1862, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 31, "name": "%alloc_201", "alias_id": 31, "names": ["%alloc_201"], "bytes_scaled": 38912, "size": 38912, "t0": 1511, "t1": 1658, "start_time": 1511, "end_time": 1658, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 32, "name": "%alloc_382", "alias_id": 32, "names": ["%alloc_382"], "bytes_scaled": 38912, "size": 38912, "t0": 4509, "t1": 4656, "start_time": 4509, "end_time": 4656, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 33, "name": "%alloc_265", "alias_id": 33, "names": ["%alloc_265"], "bytes_scaled": 38912, "size": 38912, "t0": 2569, "t1": 2708, "start_time": 2569, "end_time": 2708, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 34, "name": "%alloc_366", "alias_id": 34, "names": ["%alloc_366"], "bytes_scaled": 640, "size": 640, "t0": 4215, "t1": 5028, "start_time": 4215, "end_time": 5028, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1280, "violated_preference": false}, {"id": 35, "name": "%alloc_318", "alias_id": 35, "names": ["%alloc_318"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3483, "t1": 4452, "start_time": 3483, "end_time": 4452, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 36, "name": "%alloc_429", "alias_id": 36, "names": ["%alloc_429"], "bytes_scaled": 38912, "size": 38912, "t0": 5401, "t1": 5540, "start_time": 5401, "end_time": 5540, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 37, "name": "%alloc_278", "alias_id": 37, "names": ["%alloc_278"], "bytes_scaled": 640, "size": 640, "t0": 2779, "t1": 3632, "start_time": 2779, "end_time": 3632, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 38, "name": "%alloc_442", "alias_id": 38, "names": ["%alloc_442"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5641, "t1": 5782, "start_time": 5641, "end_time": 5782, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2495232, "violated_preference": false}, {"id": 39, "name": "%alloc_413", "alias_id": 39, "names": ["%alloc_413"], "bytes_scaled": 38912, "size": 38912, "t0": 5129, "t1": 5276, "start_time": 5129, "end_time": 5276, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 40, "name": "%alloc_422", "alias_id": 40, "names": ["%alloc_422"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5271, "t1": 5412, "start_time": 5271, "end_time": 5412, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2495232, "violated_preference": false}, {"id": 41, "name": "%alloc_148", "alias_id": 41, "names": ["%alloc_148"], "bytes_scaled": 38912, "size": 38912, "t0": 679, "t1": 1522, "start_time": 679, "end_time": 1522, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 42, "name": "%alloc_409", "alias_id": 42, "names": ["%alloc_409"], "bytes_scaled": 38912, "size": 38912, "t0": 5061, "t1": 5208, "start_time": 5061, "end_time": 5208, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 43, "name": "%alloc_181", "alias_id": 43, "names": ["%alloc_181"], "bytes_scaled": 38912, "size": 38912, "t0": 1201, "t1": 1376, "start_time": 1201, "end_time": 1376, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 44, "name": "%alloc_410", "alias_id": 44, "names": ["%alloc_410"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5067, "t1": 5208, "start_time": 5067, "end_time": 5208, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2495232, "violated_preference": false}, {"id": 45, "name": "%alloc_414", "alias_id": 45, "names": ["%alloc_414"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5135, "t1": 5276, "start_time": 5135, "end_time": 5276, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 46, "name": "%alloc_337", "alias_id": 46, "names": ["%alloc_337"], "bytes_scaled": 38912, "size": 38912, "t0": 3733, "t1": 3880, "start_time": 3733, "end_time": 3880, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 47, "name": "%alloc_425", "alias_id": 47, "names": ["%alloc_425"], "bytes_scaled": 38912, "size": 38912, "t0": 5333, "t1": 5480, "start_time": 5333, "end_time": 5480, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 48, "name": "%alloc_293", "alias_id": 48, "names": ["%alloc_293"], "bytes_scaled": 38912, "size": 38912, "t0": 3015, "t1": 3162, "start_time": 3015, "end_time": 3162, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 49, "name": "%alloc_354", "alias_id": 49, "names": ["%alloc_354"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4011, "t1": 4144, "start_time": 4011, "end_time": 4144, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 50, "name": "%alloc_370", "alias_id": 50, "names": ["%alloc_370"], "bytes_scaled": 38912, "size": 38912, "t0": 4225, "t1": 5072, "start_time": 4225, "end_time": 5072, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 51, "name": "%alloc_371", "alias_id": 51, "names": ["%alloc_371"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4233, "t1": 4452, "start_time": 4233, "end_time": 4452, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 52, "name": "%alloc_82", "alias_id": 52, "names": ["%alloc_82"], "bytes_scaled": 1245184, "size": 1245184, "t0": 179, "t1": 440, "start_time": 179, "end_time": 440, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 53, "name": "%alloc_245", "alias_id": 53, "names": ["%alloc_245"], "bytes_scaled": 38912, "size": 38912, "t0": 2229, "t1": 2376, "start_time": 2229, "end_time": 2376, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 54, "name": "%alloc_306", "alias_id": 54, "names": ["%alloc_306"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3225, "t1": 3366, "start_time": 3225, "end_time": 3366, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 55, "name": "%alloc_131", "alias_id": 55, "names": ["%alloc_131"], "bytes_scaled": 38912, "size": 38912, "t0": 517, "t1": 814, "start_time": 517, "end_time": 814, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 56, "name": "%alloc_233", "alias_id": 56, "names": ["%alloc_233"], "bytes_scaled": 38912, "size": 38912, "t0": 2053, "t1": 2914, "start_time": 2053, "end_time": 2914, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 57, "name": "%alloc_395", "alias_id": 57, "names": ["%alloc_395"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4719, "t1": 4852, "start_time": 4719, "end_time": 4852, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 58, "name": "%alloc_453", "alias_id": 58, "names": ["%alloc_453"], "bytes_scaled": 38912, "size": 38912, "t0": 5839, "t1": 5986, "start_time": 5839, "end_time": 5986, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 59, "name": "%alloc_236", "alias_id": 59, "names": ["%alloc_236"], "bytes_scaled": 640, "size": 640, "t0": 2067, "t1": 2958, "start_time": 2067, "end_time": 2958, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 60, "name": "%alloc_238", "alias_id": 60, "names": ["%alloc_238"], "bytes_scaled": 38912, "size": 38912, "t0": 2071, "t1": 2958, "start_time": 2071, "end_time": 2958, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 61, "name": "%alloc_210", "alias_id": 61, "names": ["%alloc_210"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1653, "t1": 1794, "start_time": 1653, "end_time": 1794, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 62, "name": "%alloc_465", "alias_id": 62, "names": ["%alloc_465"], "bytes_scaled": 38912, "size": 38912, "t0": 6043, "t1": 6168, "start_time": 6043, "end_time": 6168, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 63, "name": "%alloc_246", "alias_id": 63, "names": ["%alloc_246"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2235, "t1": 2376, "start_time": 2235, "end_time": 2376, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1249536, "violated_preference": false}, {"id": 64, "name": "%alloc_334", "alias_id": 64, "names": ["%alloc_334"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3671, "t1": 3812, "start_time": 3671, "end_time": 3812, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 65, "name": "%alloc_399", "alias_id": 65, "names": ["%alloc_399"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4787, "t1": 5072, "start_time": 4787, "end_time": 5072, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3742848, "violated_preference": false}, {"id": 66, "name": "%alloc_338", "alias_id": 66, "names": ["%alloc_338"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3739, "t1": 3880, "start_time": 3739, "end_time": 3880, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744128, "violated_preference": false}, {"id": 67, "name": "%alloc_192", "alias_id": 67, "names": ["%alloc_192"], "bytes_scaled": 640, "size": 640, "t0": 1349, "t1": 2240, "start_time": 1349, "end_time": 2240, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 68, "name": "%alloc_305", "alias_id": 68, "names": ["%alloc_305"], "bytes_scaled": 38912, "size": 38912, "t0": 3219, "t1": 3366, "start_time": 3219, "end_time": 3366, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 69, "name": "%alloc_128", "alias_id": 69, "names": ["%alloc_128"], "bytes_scaled": 1247616, "size": 1247616, "t0": 511, "t1": 872, "start_time": 511, "end_time": 872, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 70, "name": "%alloc_254", "alias_id": 70, "names": ["%alloc_254"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2371, "t1": 2512, "start_time": 2371, "end_time": 2512, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2497152, "violated_preference": false}, {"id": 71, "name": "%alloc_114", "alias_id": 71, "names": ["%alloc_114"], "bytes_scaled": 2490688, "size": 2490688, "t0": 417, "t1": 494, "start_time": 417, "end_time": 494, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2494080, "violated_preference": false}, {"id": 72, "name": "%alloc_111", "alias_id": 72, "names": ["%alloc_111"], "bytes_scaled": 1247616, "size": 1247616, "t0": 359, "t1": 548, "start_time": 359, "end_time": 548, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4984768, "violated_preference": false}, {"id": 73, "name": "%alloc_362", "alias_id": 73, "names": ["%alloc_362"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4201, "t1": 5140, "start_time": 4201, "end_time": 5140, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4990464, "violated_preference": false}, {"id": 74, "name": "%alloc_152", "alias_id": 74, "names": ["%alloc_152"], "bytes_scaled": 1247616, "size": 1247616, "t0": 697, "t1": 872, "start_time": 697, "end_time": 872, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 75, "name": "%alloc_186", "alias_id": 75, "names": ["%alloc_186"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1329, "t1": 2308, "start_time": 1329, "end_time": 2308, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2497152, "violated_preference": false}, {"id": 76, "name": "%alloc_122", "alias_id": 76, "names": ["%alloc_122"], "bytes_scaled": 1247616, "size": 1247616, "t0": 487, "t1": 814, "start_time": 487, "end_time": 814, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6232384, "violated_preference": false}, {"id": 77, "name": "%alloc_469", "alias_id": 77, "names": ["%alloc_469"], "bytes_scaled": 38912, "size": 38912, "t0": 6111, "t1": 6356, "start_time": 6111, "end_time": 6356, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 78, "name": "%alloc", "alias_id": 78, "names": ["%alloc"], "bytes_scaled": 1856, "size": 1856, "t0": 5, "t1": 372, "start_time": 5, "end_time": 372, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 38912, "violated_preference": false}, {"id": 79, "name": "%alloc_115", "alias_id": 79, "names": ["%alloc_115"], "bytes_scaled": 41664, "size": 41664, "t0": 369, "t1": 494, "start_time": 369, "end_time": 494, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 40768, "violated_preference": false}, {"id": 80, "name": "%alloc_230", "alias_id": 80, "names": ["%alloc_230"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2047, "t1": 3026, "start_time": 2047, "end_time": 3026, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744768, "violated_preference": false}, {"id": 81, "name": "%alloc_387", "alias_id": 81, "names": ["%alloc_387"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4583, "t1": 4724, "start_time": 4583, "end_time": 4724, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 82, "name": "%alloc_258", "alias_id": 82, "names": ["%alloc_258"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2439, "t1": 2580, "start_time": 2439, "end_time": 2580, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1249536, "violated_preference": false}, {"id": 83, "name": "%alloc_209", "alias_id": 83, "names": ["%alloc_209"], "bytes_scaled": 38912, "size": 38912, "t0": 1647, "t1": 1794, "start_time": 1647, "end_time": 1794, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 84, "name": "%alloc_421", "alias_id": 84, "names": ["%alloc_421"], "bytes_scaled": 38912, "size": 38912, "t0": 5265, "t1": 5412, "start_time": 5265, "end_time": 5412, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 85, "name": "%alloc_169", "alias_id": 85, "names": ["%alloc_169"], "bytes_scaled": 38912, "size": 38912, "t0": 997, "t1": 1144, "start_time": 997, "end_time": 1144, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 86, "name": "%alloc_121", "alias_id": 86, "names": ["%alloc_121"], "bytes_scaled": 68096, "size": 68096, "t0": 437, "t1": 5028, "start_time": 437, "end_time": 5028, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7480000, "violated_preference": false}, {"id": 87, "name": "%alloc_326", "alias_id": 87, "names": ["%alloc_326"], "bytes_scaled": 38912, "size": 38912, "t0": 3507, "t1": 4384, "start_time": 3507, "end_time": 4384, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 88, "name": "%alloc_218", "alias_id": 88, "names": ["%alloc_218"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1789, "t1": 1930, "start_time": 1789, "end_time": 1930, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744768, "violated_preference": false}, {"id": 89, "name": "%alloc_298", "alias_id": 89, "names": ["%alloc_298"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3089, "t1": 3230, "start_time": 3089, "end_time": 3230, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 90, "name": "%alloc_182", "alias_id": 90, "names": ["%alloc_182"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1207, "t1": 1522, "start_time": 1207, "end_time": 1522, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 91, "name": "%alloc_350", "alias_id": 91, "names": ["%alloc_350"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3943, "t1": 4084, "start_time": 3943, "end_time": 4084, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744128, "violated_preference": false}, {"id": 92, "name": "%alloc_345", "alias_id": 92, "names": ["%alloc_345"], "bytes_scaled": 38912, "size": 38912, "t0": 3869, "t1": 4016, "start_time": 3869, "end_time": 4016, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 93, "name": "%alloc_166", "alias_id": 93, "names": ["%alloc_166"], "bytes_scaled": 1247616, "size": 1247616, "t0": 935, "t1": 1076, "start_time": 935, "end_time": 1076, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 94, "name": "%alloc_398", "alias_id": 94, "names": ["%alloc_398"], "bytes_scaled": 38912, "size": 38912, "t0": 4781, "t1": 4926, "start_time": 4781, "end_time": 4926, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 95, "name": "%alloc_461", "alias_id": 95, "names": ["%alloc_461"], "bytes_scaled": 38912, "size": 38912, "t0": 5975, "t1": 6114, "start_time": 5975, "end_time": 6114, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 96, "name": "%alloc_433", "alias_id": 96, "names": ["%alloc_433"], "bytes_scaled": 38912, "size": 38912, "t0": 5469, "t1": 5602, "start_time": 5469, "end_time": 5602, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 97, "name": "%alloc_403", "alias_id": 97, "names": ["%alloc_403"], "bytes_scaled": 640, "size": 640, "t0": 4909, "t1": 5646, "start_time": 4909, "end_time": 5646, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6238080, "violated_preference": false}, {"id": 98, "name": "%alloc_253", "alias_id": 98, "names": ["%alloc_253"], "bytes_scaled": 38912, "size": 38912, "t0": 2365, "t1": 2512, "start_time": 2365, "end_time": 2512, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 99, "name": "%alloc_297", "alias_id": 99, "names": ["%alloc_297"], "bytes_scaled": 38912, "size": 38912, "t0": 3083, "t1": 3230, "start_time": 3083, "end_time": 3230, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 100, "name": "%alloc_162", "alias_id": 100, "names": ["%alloc_162"], "bytes_scaled": 1247616, "size": 1247616, "t0": 867, "t1": 1008, "start_time": 867, "end_time": 1008, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744128, "violated_preference": false}, {"id": 101, "name": "%alloc_309", "alias_id": 101, "names": ["%alloc_309"], "bytes_scaled": 38912, "size": 38912, "t0": 3287, "t1": 3426, "start_time": 3287, "end_time": 3426, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 102, "name": "%alloc_466", "alias_id": 102, "names": ["%alloc_466"], "bytes_scaled": 1247616, "size": 1247616, "t0": 6049, "t1": 6168, "start_time": 6049, "end_time": 6168, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 103, "name": "%alloc_353", "alias_id": 103, "names": ["%alloc_353"], "bytes_scaled": 38912, "size": 38912, "t0": 4005, "t1": 4144, "start_time": 4005, "end_time": 4144, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 104, "name": "%alloc_262", "alias_id": 104, "names": ["%alloc_262"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2507, "t1": 2648, "start_time": 2507, "end_time": 2648, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4992384, "violated_preference": false}, {"id": 105, "name": "%alloc_462", "alias_id": 105, "names": ["%alloc_462"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5981, "t1": 6114, "start_time": 5981, "end_time": 6114, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 106, "name": "%alloc_146", "alias_id": 106, "names": ["%alloc_146"], "bytes_scaled": 640, "size": 640, "t0": 675, "t1": 1522, "start_time": 675, "end_time": 1522, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4991744, "violated_preference": false}, {"id": 107, "name": "%alloc_283", "alias_id": 107, "names": ["%alloc_283"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2797, "t1": 3744, "start_time": 2797, "end_time": 3744, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4992384, "violated_preference": false}, {"id": 108, "name": "%alloc_99", "alias_id": 108, "names": ["%alloc_99"], "bytes_scaled": 19456, "size": 19456, "t0": 271, "t1": 440, "start_time": 271, "end_time": 440, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 82432, "violated_preference": false}, {"id": 109, "name": "%alloc_286", "alias_id": 109, "names": ["%alloc_286"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2807, "t1": 3026, "start_time": 2807, "end_time": 3026, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 110, "name": "%alloc_106", "alias_id": 110, "names": ["%alloc_106"], "bytes_scaled": 19456, "size": 19456, "t0": 343, "t1": 6356, "start_time": 343, "end_time": 6356, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7548096, "violated_preference": false}, {"id": 111, "name": "%alloc_190", "alias_id": 111, "names": ["%alloc_190"], "bytes_scaled": 640, "size": 640, "t0": 1343, "t1": 2196, "start_time": 1343, "end_time": 2196, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 112, "name": "%alloc_282", "alias_id": 112, "names": ["%alloc_282"], "bytes_scaled": 38912, "size": 38912, "t0": 2789, "t1": 3676, "start_time": 2789, "end_time": 3676, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 113, "name": "%alloc_198", "alias_id": 113, "names": ["%alloc_198"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1371, "t1": 1590, "start_time": 1371, "end_time": 1590, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4992384, "violated_preference": false}, {"id": 114, "name": "%alloc_170", "alias_id": 114, "names": ["%alloc_170"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1003, "t1": 1144, "start_time": 1003, "end_time": 1144, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 640, "violated_preference": false}, {"id": 115, "name": "%alloc_342", "alias_id": 115, "names": ["%alloc_342"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3807, "t1": 3948, "start_time": 3807, "end_time": 3948, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4991744, "violated_preference": false}, {"id": 116, "name": "%alloc_438", "alias_id": 116, "names": ["%alloc_438"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5597, "t1": 5714, "start_time": 5597, "end_time": 5714, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3742848, "violated_preference": false}, {"id": 117, "name": "%alloc_195", "alias_id": 117, "names": ["%alloc_195"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1361, "t1": 2308, "start_time": 1361, "end_time": 2308, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 118, "name": "%alloc_277", "alias_id": 118, "names": ["%alloc_277"], "bytes_scaled": 38912, "size": 38912, "t0": 2771, "t1": 3632, "start_time": 2771, "end_time": 3632, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 119, "name": "%alloc_242", "alias_id": 119, "names": ["%alloc_242"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2089, "t1": 2308, "start_time": 2089, "end_time": 2308, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4992384, "violated_preference": false}, {"id": 120, "name": "%alloc_333", "alias_id": 120, "names": ["%alloc_333"], "bytes_scaled": 38912, "size": 38912, "t0": 3665, "t1": 3812, "start_time": 3665, "end_time": 3812, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 121, "name": "%alloc_185", "alias_id": 121, "names": ["%alloc_185"], "bytes_scaled": 38912, "size": 38912, "t0": 1269, "t1": 1590, "start_time": 1269, "end_time": 1590, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 122, "name": "%alloc_358", "alias_id": 122, "names": ["%alloc_358"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4079, "t1": 4384, "start_time": 4079, "end_time": 4384, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 123, "name": "%alloc_234", "alias_id": 123, "names": ["%alloc_234"], "bytes_scaled": 640, "size": 640, "t0": 2061, "t1": 2914, "start_time": 2061, "end_time": 2914, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6240000, "violated_preference": false}, {"id": 124, "name": "%alloc_365", "alias_id": 124, "names": ["%alloc_365"], "bytes_scaled": 38912, "size": 38912, "t0": 4207, "t1": 5028, "start_time": 4207, "end_time": 5028, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 125, "name": "%alloc_324", "alias_id": 125, "names": ["%alloc_324"], "bytes_scaled": 640, "size": 640, "t0": 3503, "t1": 4384, "start_time": 3503, "end_time": 4384, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1920, "violated_preference": false}, {"id": 126, "name": "%alloc_221", "alias_id": 126, "names": ["%alloc_221"], "bytes_scaled": 38912, "size": 38912, "t0": 1851, "t1": 1990, "start_time": 1851, "end_time": 1990, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 38912, "violated_preference": false}, {"id": 127, "name": "%alloc_100", "alias_id": 127, "names": ["%alloc_100"], "bytes_scaled": 19456, "size": 19456, "t0": 275, "t1": 494, "start_time": 275, "end_time": 494, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 101888, "violated_preference": false}, {"id": 128, "name": "%alloc_317", "alias_id": 128, "names": ["%alloc_317"], "bytes_scaled": 38912, "size": 38912, "t0": 3423, "t1": 3744, "start_time": 3423, "end_time": 3744, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 129, "name": "%alloc_346", "alias_id": 129, "names": ["%alloc_346"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3875, "t1": 4016, "start_time": 3875, "end_time": 4016, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 130, "name": "%alloc_0", "alias_id": 130, "names": ["%alloc_0"], "bytes_scaled": 2490368, "size": 2490368, "t0": 923, "t1": 6452, "start_time": 923, "end_time": 6452, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 8815168, "violated_preference": false}, {"id": 131, "name": "%alloc_405", "alias_id": 131, "names": ["%alloc_405"], "bytes_scaled": 38912, "size": 38912, "t0": 4913, "t1": 5646, "start_time": 4913, "end_time": 5646, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 132, "name": "%alloc_383", "alias_id": 132, "names": ["%alloc_383"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4515, "t1": 4656, "start_time": 4515, "end_time": 4656, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 133, "name": "%alloc_239", "alias_id": 133, "names": ["%alloc_239"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2079, "t1": 3026, "start_time": 2079, "end_time": 3026, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 11305536, "violated_preference": false}, {"id": 134, "name": "%alloc_270", "alias_id": 134, "names": ["%alloc_270"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2643, "t1": 2958, "start_time": 2643, "end_time": 2958, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1249536, "violated_preference": false}, {"id": 135, "name": "%alloc_108", "alias_id": 135, "names": ["%alloc_108"], "bytes_scaled": 55360, "size": 55360, "t0": 329, "t1": 440, "start_time": 329, "end_time": 440, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 121344, "violated_preference": false}, {"id": 136, "name": "%alloc_391", "alias_id": 136, "names": ["%alloc_391"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4651, "t1": 4792, "start_time": 4651, "end_time": 4792, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 137, "name": "%alloc_327", "alias_id": 137, "names": ["%alloc_327"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3515, "t1": 4452, "start_time": 3515, "end_time": 4452, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 11305536, "violated_preference": false}, {"id": 138, "name": "%alloc_174", "alias_id": 138, "names": ["%alloc_174"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1071, "t1": 1212, "start_time": 1071, "end_time": 1212, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 139, "name": "%alloc_137", "alias_id": 139, "names": ["%alloc_137"], "bytes_scaled": 1247616, "size": 1247616, "t0": 543, "t1": 872, "start_time": 543, "end_time": 872, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 140, "name": "%alloc_189", "alias_id": 140, "names": ["%alloc_189"], "bytes_scaled": 38912, "size": 38912, "t0": 1335, "t1": 2196, "start_time": 1335, "end_time": 2196, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 141, "name": "%alloc_301", "alias_id": 141, "names": ["%alloc_301"], "bytes_scaled": 38912, "size": 38912, "t0": 3151, "t1": 3298, "start_time": 3151, "end_time": 3298, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 142, "name": "%alloc_390", "alias_id": 142, "names": ["%alloc_390"], "bytes_scaled": 38912, "size": 38912, "t0": 4645, "t1": 4792, "start_time": 4645, "end_time": 4792, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 143, "name": "%alloc_205", "alias_id": 143, "names": ["%alloc_205"], "bytes_scaled": 38912, "size": 38912, "t0": 1579, "t1": 1726, "start_time": 1579, "end_time": 1726, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 144, "name": "%alloc_165", "alias_id": 144, "names": ["%alloc_165"], "bytes_scaled": 38912, "size": 38912, "t0": 929, "t1": 1076, "start_time": 929, "end_time": 1076, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 145, "name": "%alloc_107", "alias_id": 145, "names": ["%alloc_107"], "bytes_scaled": 19456, "size": 19456, "t0": 343, "t1": 6356, "start_time": 343, "end_time": 6356, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 12553152, "violated_preference": false}, {"id": 146, "name": "%alloc_9", "alias_id": 146, "names": ["%alloc_9"], "bytes_scaled": 155648, "size": 155648, "t0": 841, "t1": 6290, "start_time": 841, "end_time": 6290, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6240640, "violated_preference": false}, {"id": 147, "name": "%alloc_119", "alias_id": 147, "names": ["%alloc_119"], "bytes_scaled": 622592, "size": 622592, "t0": 479, "t1": 6168, "start_time": 479, "end_time": 6168, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 233472, "violated_preference": false}, {"id": 148, "name": "%alloc_136", "alias_id": 148, "names": ["%alloc_136"], "bytes_scaled": 38912, "size": 38912, "t0": 535, "t1": 872, "start_time": 535, "end_time": 872, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 149, "name": "%alloc_161", "alias_id": 149, "names": ["%alloc_161"], "bytes_scaled": 38912, "size": 38912, "t0": 861, "t1": 1008, "start_time": 861, "end_time": 1008, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 150, "name": "%alloc_290", "alias_id": 150, "names": ["%alloc_290"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2953, "t1": 3094, "start_time": 2953, "end_time": 3094, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2497152, "violated_preference": false}, {"id": 151, "name": "%alloc_349", "alias_id": 151, "names": ["%alloc_349"], "bytes_scaled": 38912, "size": 38912, "t0": 3937, "t1": 4084, "start_time": 3937, "end_time": 4084, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 152, "name": "%alloc_289", "alias_id": 152, "names": ["%alloc_289"], "bytes_scaled": 38912, "size": 38912, "t0": 2947, "t1": 3094, "start_time": 2947, "end_time": 3094, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 153, "name": "%alloc_156", "alias_id": 153, "names": ["%alloc_156"], "bytes_scaled": 13120, "size": 13120, "t0": 811, "t1": 6356, "start_time": 811, "end_time": 6356, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 12572608, "violated_preference": false}, {"id": 154, "name": "%alloc_437", "alias_id": 154, "names": ["%alloc_437"], "bytes_scaled": 38912, "size": 38912, "t0": 5537, "t1": 5714, "start_time": 5537, "end_time": 5714, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 155, "name": "%alloc_302", "alias_id": 155, "names": ["%alloc_302"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3157, "t1": 3298, "start_time": 3157, "end_time": 3298, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2496512, "violated_preference": false}, {"id": 156, "name": "%alloc_458", "alias_id": 156, "names": ["%alloc_458"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5913, "t1": 6054, "start_time": 5913, "end_time": 6054, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2495232, "violated_preference": false}, {"id": 157, "name": "%alloc_149", "alias_id": 157, "names": ["%alloc_149"], "bytes_scaled": 1247616, "size": 1247616, "t0": 687, "t1": 1590, "start_time": 687, "end_time": 1590, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 11305536, "violated_preference": false}, {"id": 158, "name": "%alloc_314", "alias_id": 158, "names": ["%alloc_314"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3361, "t1": 3676, "start_time": 3361, "end_time": 3676, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744128, "violated_preference": false}, {"id": 159, "name": "%alloc_375", "alias_id": 159, "names": ["%alloc_375"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4379, "t1": 4520, "start_time": 4379, "end_time": 4520, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 12585728, "violated_preference": false}, {"id": 160, "name": "%alloc_177", "alias_id": 160, "names": ["%alloc_177"], "bytes_scaled": 38912, "size": 38912, "t0": 1133, "t1": 1272, "start_time": 1133, "end_time": 1272, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 155648, "violated_preference": false}, {"id": 161, "name": "%alloc_229", "alias_id": 161, "names": ["%alloc_229"], "bytes_scaled": 38912, "size": 38912, "t0": 1987, "t1": 2308, "start_time": 1987, "end_time": 2308, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 162, "name": "%alloc_417", "alias_id": 162, "names": ["%alloc_417"], "bytes_scaled": 38912, "size": 38912, "t0": 5197, "t1": 5344, "start_time": 5197, "end_time": 5344, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 163, "name": "%alloc_173", "alias_id": 163, "names": ["%alloc_173"], "bytes_scaled": 38912, "size": 38912, "t0": 1065, "t1": 1212, "start_time": 1065, "end_time": 1212, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 164, "name": "%alloc_378", "alias_id": 164, "names": ["%alloc_378"], "bytes_scaled": 38912, "size": 38912, "t0": 4441, "t1": 4588, "start_time": 4441, "end_time": 4588, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 165, "name": "%alloc_116", "alias_id": 165, "names": ["%alloc_116"], "bytes_scaled": 1247616, "size": 1247616, "t0": 425, "t1": 708, "start_time": 425, "end_time": 708, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 8815168, "violated_preference": false}, {"id": 166, "name": "%alloc_454", "alias_id": 166, "names": ["%alloc_454"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5845, "t1": 5986, "start_time": 5845, "end_time": 5986, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3742848, "violated_preference": false}, {"id": 167, "name": "%alloc_214", "alias_id": 167, "names": ["%alloc_214"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1721, "t1": 1862, "start_time": 1721, "end_time": 1862, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4992384, "violated_preference": false}, {"id": 168, "name": "%alloc_217", "alias_id": 168, "names": ["%alloc_217"], "bytes_scaled": 38912, "size": 38912, "t0": 1783, "t1": 1930, "start_time": 1783, "end_time": 1930, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 169, "name": "%alloc_134", "alias_id": 169, "names": ["%alloc_134"], "bytes_scaled": 640, "size": 640, "t0": 531, "t1": 872, "start_time": 531, "end_time": 872, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 10062784, "violated_preference": false}, {"id": 170, "name": "%alloc_430", "alias_id": 170, "names": ["%alloc_430"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5407, "t1": 5540, "start_time": 5407, "end_time": 5540, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3742848, "violated_preference": false}, {"id": 171, "name": "%alloc_155", "alias_id": 171, "names": ["%alloc_155"], "bytes_scaled": 5184, "size": 5184, "t0": 705, "t1": 5646, "start_time": 705, "end_time": 5646, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 13833344, "violated_preference": false}, {"id": 172, "name": "%alloc_450", "alias_id": 172, "names": ["%alloc_450"], "bytes_scaled": 1247616, "size": 1247616, "t0": 5777, "t1": 5918, "start_time": 5777, "end_time": 5918, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1247616, "violated_preference": false}, {"id": 173, "name": "%alloc_194", "alias_id": 173, "names": ["%alloc_194"], "bytes_scaled": 38912, "size": 38912, "t0": 1353, "t1": 2240, "start_time": 1353, "end_time": 2240, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 856064, "violated_preference": false}, {"id": 174, "name": "%alloc_120", "alias_id": 174, "names": ["%alloc_120"], "bytes_scaled": 19776, "size": 19776, "t0": 479, "t1": 6356, "start_time": 479, "end_time": 6356, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 13838528, "violated_preference": false}, {"id": 175, "name": "%alloc_406", "alias_id": 175, "names": ["%alloc_406"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4921, "t1": 5140, "start_time": 4921, "end_time": 5140, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 176, "name": "%alloc_294", "alias_id": 176, "names": ["%alloc_294"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3021, "t1": 3162, "start_time": 3021, "end_time": 3162, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 177, "name": "%alloc_202", "alias_id": 177, "names": ["%alloc_202"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1517, "t1": 1658, "start_time": 1517, "end_time": 1658, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 12585728, "violated_preference": false}, {"id": 178, "name": "%alloc_280", "alias_id": 178, "names": ["%alloc_280"], "bytes_scaled": 640, "size": 640, "t0": 2785, "t1": 3676, "start_time": 2785, "end_time": 3676, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6396288, "violated_preference": false}, {"id": 179, "name": "%alloc_178", "alias_id": 179, "names": ["%alloc_178"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1139, "t1": 1272, "start_time": 1139, "end_time": 1272, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3744128, "violated_preference": false}, {"id": 180, "name": "%alloc_313", "alias_id": 180, "names": ["%alloc_313"], "bytes_scaled": 38912, "size": 38912, "t0": 3355, "t1": 3530, "start_time": 3355, "end_time": 3530, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 116736, "violated_preference": false}, {"id": 181, "name": "%alloc_132", "alias_id": 181, "names": ["%alloc_132"], "bytes_scaled": 640, "size": 640, "t0": 525, "t1": 814, "start_time": 525, "end_time": 814, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1280, "violated_preference": false}, {"id": 182, "name": "%alloc_274", "alias_id": 182, "names": ["%alloc_274"], "bytes_scaled": 1247616, "size": 1247616, "t0": 2765, "t1": 3744, "start_time": 2765, "end_time": 3744, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 183, "name": "%alloc_20", "alias_id": 183, "names": ["%alloc_20"], "bytes_scaled": 155648, "size": 155648, "t0": 647, "t1": 6356, "start_time": 647, "end_time": 6356, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 13858304, "violated_preference": false}, {"id": 184, "name": "%alloc_222", "alias_id": 184, "names": ["%alloc_222"], "bytes_scaled": 1247616, "size": 1247616, "t0": 1857, "t1": 1990, "start_time": 1857, "end_time": 1990, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1248896, "violated_preference": false}, {"id": 185, "name": "%alloc_225", "alias_id": 185, "names": ["%alloc_225"], "bytes_scaled": 38912, "size": 38912, "t0": 1919, "t1": 2094, "start_time": 1919, "end_time": 2094, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 186, "name": "%alloc_257", "alias_id": 186, "names": ["%alloc_257"], "bytes_scaled": 38912, "size": 38912, "t0": 2433, "t1": 2580, "start_time": 2433, "end_time": 2580, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 187, "name": "%alloc_489", "alias_id": 187, "names": ["%alloc_489"], "bytes_scaled": 960, "size": 960, "t0": 6453, "t1": 6464, "start_time": 6453, "end_time": 6464, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 188, "name": "%alloc_273", "alias_id": 188, "names": ["%alloc_273"], "bytes_scaled": 38912, "size": 38912, "t0": 2705, "t1": 3026, "start_time": 2705, "end_time": 3026, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 856064, "violated_preference": false}, {"id": 189, "name": "%alloc_379", "alias_id": 189, "names": ["%alloc_379"], "bytes_scaled": 1247616, "size": 1247616, "t0": 4447, "t1": 4588, "start_time": 4447, "end_time": 4588, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 7567552, "violated_preference": false}, {"id": 190, "name": "%alloc_330", "alias_id": 190, "names": ["%alloc_330"], "bytes_scaled": 1247616, "size": 1247616, "t0": 3525, "t1": 3744, "start_time": 3525, "end_time": 3744, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 12585728, "violated_preference": false}, {"id": 191, "name": "%alloc_321", "alias_id": 191, "names": ["%alloc_321"], "bytes_scaled": 38912, "size": 38912, "t0": 3489, "t1": 4340, "start_time": 3489, "end_time": 4340, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 856064, "violated_preference": false}, {"id": 192, "name": "%alloc_357", "alias_id": 192, "names": ["%alloc_357"], "bytes_scaled": 38912, "size": 38912, "t0": 4073, "t1": 4238, "start_time": 4073, "end_time": 4238, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 194560, "violated_preference": false}, {"id": 193, "name": "%alloc_140", "alias_id": 193, "names": ["%alloc_140"], "bytes_scaled": 1247616, "size": 1247616, "t0": 655, "t1": 1590, "start_time": 655, "end_time": 1590, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 14013952, "violated_preference": false}, {"id": 194, "name": "%alloc_402", "alias_id": 194, "names": ["%alloc_402"], "bytes_scaled": 38912, "size": 38912, "t0": 4849, "t1": 5140, "start_time": 4849, "end_time": 5140, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 77824, "violated_preference": false}, {"id": 195, "name": "%alloc_269", "alias_id": 195, "names": ["%alloc_269"], "bytes_scaled": 38912, "size": 38912, "t0": 2637, "t1": 2812, "start_time": 2637, "end_time": 2812, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 894976, "violated_preference": false}], "failures": [], "summary": {"original_total_bytes": 46490624, "scaled_total_bytes": 110416000, "local_peak_bytes": 856064, "weight_peak_bytes": 11682624}};
    const localCap = data.config ? data.config.local_cap : (3*1024*1024);
    const weightCap = data.config ? data.config.weight_cap : (16*1024*1024);
    const LOCAL_MB = (localCap/(1024*1024)).toFixed(0);
    const WEIGHT_MB = (weightCap/(1024*1024)).toFixed(0);

    // State
    let viewRange = 1000;
    let viewOffset = 0;

    // Build pool-specific arrays
    const allocs = (data.allocations || []).map(a => ({
      name: a.name || (a.names && a.names[0]) || ('alloc_'+a.alias_id),
      t0: a.start_time ?? a.t0, t1: a.end_time ?? a.t1,
      size: a.size ?? a.bytes_scaled ?? 0,
      pool: a.pool || 'weight', offset: a.offset || 0,
      violated: !!a.violated_preference, is_explicit: !!a.is_explicit
    }));
    const allocsLocal = allocs.filter(a => a.pool === 'local');
    const allocsWeight = allocs.filter(a => a.pool === 'weight');

    // Elements
    const canvasLocal = document.getElementById('canvasLocal');
    const canvasWeight = document.getElementById('canvasWeight');
    const ctxLocal = canvasLocal.getContext('2d');
    const ctxWeight = canvasWeight.getContext('2d');
    const detailPanel = document.getElementById('detailPanel');

    // Resize canvases based on CSS
    function setupCanvas(canvas) {
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      const w = Math.max(800, rect.width);
      const h = 260;
      canvas.width = w * dpr; canvas.height = h * dpr;
      canvas.style.width = w + 'px'; canvas.style.height = h + 'px';
      const ctx = canvas.getContext('2d');
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    function timeToX(t, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerW = rect.width - 80;
      return 60 + ( (t - viewOffset) / viewRange ) * innerW;
    }
    function addrToY(addr, cap, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerH = rect.height - 60;
      return rect.height - 30 - (addr / cap) * innerH;
    }

    function drawGrid(ctx, canvas, capMB) {
      const rect = canvas.getBoundingClientRect();
      ctx.clearRect(0,0,rect.width,rect.height);
      ctx.fillStyle = '#fff'; ctx.fillRect(0,0,rect.width,rect.height);
      ctx.strokeStyle = '#e0e0e0'; ctx.lineWidth = 0.5; ctx.setLineDash([2,2]);
      const innerW = rect.width - 80;
      const innerH = rect.height - 60;
      // vertical time grid (10 ticks)
      const step = Math.max(1, Math.floor(viewRange/10));
      for (let t = Math.ceil(viewOffset/step)*step; t <= viewOffset+viewRange; t+=step) {
        const x = 60 + ( (t - viewOffset) / viewRange ) * innerW;
        ctx.beginPath(); ctx.moveTo(x, 20); ctx.lineTo(x, rect.height-20); ctx.stroke();
        ctx.fillStyle = '#666'; ctx.font = '11px Arial'; ctx.textAlign = 'center';
        ctx.fillText(t.toString(), x, rect.height-5);
      }
      // horizontal addr grid: every 1MB
      ctx.setLineDash([1,0]);
      ctx.strokeStyle = '#bdbdbd'; ctx.lineWidth = 1;
      for (let mb=1; mb<=capMB; ++mb) {
        const y = addrToY(mb*1024*1024, capMB*1024*1024, canvas);
        ctx.beginPath(); ctx.moveTo(60, y); ctx.lineTo(rect.width-20, y); ctx.stroke();
      }
      // axes
      ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.setLineDash([]);
      ctx.beginPath(); ctx.moveTo(60, 20); ctx.lineTo(60, rect.height-20); ctx.stroke();
      ctx.beginPath(); ctx.moveTo(60, rect.height-20); ctx.lineTo(rect.width-20, rect.height-20); ctx.stroke();
    }

    function drawPool(ctx, canvas, capBytes, items) {
      drawGrid(ctx, canvas, (capBytes/1024/1024)|0);
      const rect = canvas.getBoundingClientRect();
      items.forEach(a => {
        // skip if out of range
        if (a.t1 < viewOffset || a.t0 > viewOffset+viewRange) return;
        const x1 = Math.max(60, timeToX(a.t0, canvas));
        const x2 = Math.min(rect.width-20, timeToX(a.t1, canvas));
        if (x2 <= x1) return;
        const y = addrToY(a.offset + a.size, capBytes, canvas);
        const h = (a.size / capBytes) * (rect.height - 60);
        const fill = a.is_explicit ? '#FF5722' : (a.violated ? '#FFD700' : '#2196F3');
        ctx.fillStyle = fill;
        ctx.fillRect(x1, y, x2-x1, h);
        ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.strokeRect(x1, y, x2-x1, h);
        // store bounds for hit test
        a._bounds = {x:x1, y:y, w:(x2-x1), h:h, canvas:canvas};
      });
    }

    function renderAll() {
      setupCanvas(canvasLocal); setupCanvas(canvasWeight);
      drawPool(ctxLocal, canvasLocal, localCap, allocsLocal);
      drawPool(ctxWeight, canvasWeight, weightCap, allocsWeight);
      // stats
      document.getElementById('statTotal').textContent = allocs.length.toString();
      document.getElementById('statPV').textContent = allocs.filter(a=>a.violated).length.toString();
      const peakLocal = peakUsage(allocsLocal);
      const peakWeight = peakUsage(allocsWeight);
      document.getElementById('statLocalPeak').textContent = (peakLocal/1024/1024).toFixed(2)+ 'MB';
      document.getElementById('statWeightPeak').textContent = (peakWeight/1024/1024).toFixed(2)+ 'MB';
    }

    function peakUsage(items){
      const ev=[]; items.forEach(a=>{ ev.push({t:a.t0, d:+a.size}); ev.push({t:a.t1, d:-a.size}); });
      ev.sort((x,y)=>x.t-y.t); let cur=0, peak=0; ev.forEach(e=>{cur+=e.d; if(cur>peak) peak=cur;}); return peak;
    }

    function hitTest(canvas, x, y) {
      const list = (canvas===canvasLocal) ? allocsLocal : allocsWeight;
      for (let a of list) {
        if (!a._bounds) continue;
        const b=a._bounds; if (x>=b.x && x<=b.x+b.w && y>=b.y && y<=b.y+b.h) return a;
      }
      return null;
    }

    function showDetail(a){
      const mb=(a.size/1024/1024).toFixed(3);
      detailPanel.innerHTML = `
        <div><b>${a.name}</b></div>
        <div>Pool: ${a.pool}</div>
        <div>Time: [${a.t0} - ${a.t1}]</div>
        <div>Offset: ${a.offset} (bytes)</div>
        <div>Size: ${mb} MB</div>
        <div>Explicit: ${a.is_explicit}</div>
        <div>Pref. Violated: ${a.violated}</div>
      `;
    }

    function onCanvasClick(ev, canvas){
      const rect = canvas.getBoundingClientRect();
      const x = ev.clientX - rect.left; const y = ev.clientY - rect.top;
      const a = hitTest(canvas, x, y); if (a) showDetail(a);
    }

    // UI bindings
    document.getElementById('viewRange').addEventListener('input', e=>{
      viewRange = parseInt(e.target.value); document.getElementById('viewRangeValue').textContent=viewRange; renderAll();
    });
    document.getElementById('viewOffset').addEventListener('input', e=>{
      viewOffset = parseInt(e.target.value); document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    document.getElementById('resetBtn').addEventListener('click', ()=>{
      viewRange=1000; viewOffset=0; document.getElementById('viewRange').value=viewRange; document.getElementById('viewRangeValue').textContent=viewRange; document.getElementById('viewOffset').value=viewOffset; document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    canvasLocal.addEventListener('click', ev=> onCanvasClick(ev, canvasLocal));
    canvasWeight.addEventListener('click', ev=> onCanvasClick(ev, canvasWeight));

    // Initial
    window.addEventListener('resize', renderAll);
    renderAll();
  </script>
</body>
</html>
