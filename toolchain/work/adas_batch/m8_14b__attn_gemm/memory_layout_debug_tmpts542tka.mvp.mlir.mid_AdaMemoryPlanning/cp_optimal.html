<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>AdaS SRAM Allocation (Dual Canvas)</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin:0; background:#f5f5f5; }
    .header { padding: 16px 20px; background: white; border-bottom: 1px solid #e1e4e8; }
    .header h1 { margin:0; font-size: 20px; color:#333; }
    .main { display:flex; gap: 0; }
    .left { flex: 1; padding: 16px; }
    .panel { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; margin-bottom:16px; }
    .row { display:flex; gap:12px; align-items:center; flex-wrap:wrap; }
    .stat { display:flex; gap:16px; }
    .stat .box { background:#f8f9fa; padding:8px 10px; border-radius:4px; border-left:3px solid #4CAF50; }
    .canvasWrap { background:white; border:1px solid #e1e4e8; border-radius:4px; padding:8px; margin-bottom:12px; }
    .canvasHeader { display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; color:#333; }
    .legend { display:flex; gap:16px; }
    .legend .item { display:flex; gap:6px; align-items:center; }
    .colorBox { width:14px; height:10px; border-radius:2px; }
    .detail { background:white; border:1px solid #e1e4e8; border-radius:6px; padding:12px; }
    canvas { width: 100%; height: 260px; display:block; background:#fff; }
    .note { color:#666; font-size:12px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>AdaS SRAM Memory Allocation (Dual Canvas)</h1>
    <div class="note">双画布同步：上=Local (3MB)，下=Weight (16MB)。左右滚动/缩放同步；点击条块显示详情。</div>
  </div>
  <div class="main">
    <div class="left" style="width:100%">
      <div class="panel">
        <div class="row">
          <label>View Range:</label>
          <input type="range" id="viewRange" min="100" max="5000" value="1000" step="100"/>
          <span id="viewRangeValue">1000</span>
          <label>View Offset:</label>
          <input type="range" id="viewOffset" min="0" max="10000" value="0" step="10"/>
          <span id="viewOffsetValue">0</span>
          <button id="resetBtn">Reset</button>
          <div class="legend">
            <div class="item"><div class="colorBox" style="background:#2196F3"></div>Normal</div>
            <div class="item"><div class="colorBox" style="background:#FFD700"></div>Pref Violation</div>
            <div class="item"><div class="colorBox" style="background:#FF5722"></div>Explicit</div>
          </div>
        </div>
        <div class="stat" style="margin-top:8px">
          <div class="box">Total: <b id="statTotal">0</b></div>
          <div class="box">Local Peak: <b id="statLocalPeak">0MB</b></div>
          <div class="box">Weight Peak: <b id="statWeightPeak">0MB</b></div>
          <div class="box">Pref Violations: <b id="statPV">0</b></div>
        </div>
      </div>

      <div class="canvasWrap">
        <div class="canvasHeader"><b>Local Pool (3MB)</b><span class="note">Y: 0..3MB</span></div>
        <canvas id="canvasLocal"></canvas>
      </div>
      <div class="canvasWrap">
        <div class="canvasHeader"><b>Weight Pool (16MB)</b><span class="note">Y: 0..16MB</span></div>
        <canvas id="canvasWeight"></canvas>
      </div>
      <div class="detail" id="detailPanel">点击条块查看详细信息</div>
    </div>
  </div>

  <script>
    const data = {"status": "success", "config": {"local_cap": 3145728, "weight_cap": 16777216, "weight_base": 4194304, "local_scale": 0.375, "weight_scale": 1.0, "granularity": 64, "scale_explicit": "keep", "max_solve_seconds": 300, "workers": 0, "mode": "optimal", "objective": "cost"}, "stats": {"total_tasks": 40, "allocated": 40, "failed": 0, "preference_violation_cost": 0, "preference_violation_bytes": 0, "preference_violation_count": 0}, "allocations": [{"id": 0, "name": "%alloc_157", "alias_id": 0, "names": ["%alloc_157"], "bytes_scaled": 3136, "size": 3136, "t0": 901, "t1": 1100, "start_time": 901, "end_time": 1100, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 1, "name": "%alloc_151", "alias_id": 1, "names": ["%alloc_151"], "bytes_scaled": 6208, "size": 6208, "t0": 867, "t1": 1168, "start_time": 867, "end_time": 1168, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 3136, "violated_preference": false}, {"id": 2, "name": "%alloc_19", "alias_id": 2, "names": ["%alloc_19"], "bytes_scaled": 196608, "size": 196608, "t0": 55, "t1": 580, "start_time": 55, "end_time": 580, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 3, "name": "%alloc_152", "alias_id": 3, "names": ["%alloc_152"], "bytes_scaled": 512, "size": 512, "t0": 863, "t1": 1004, "start_time": 863, "end_time": 1004, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 9344, "violated_preference": false}, {"id": 4, "name": "%alloc_94", "alias_id": 4, "names": ["%alloc_94"], "bytes_scaled": 2099200, "size": 2099200, "t0": 441, "t1": 580, "start_time": 441, "end_time": 580, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 5, "name": "%alloc_91", "alias_id": 5, "names": ["%alloc_91"], "bytes_scaled": 2099200, "size": 2099200, "t0": 427, "t1": 468, "start_time": 427, "end_time": 468, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099200, "violated_preference": false}, {"id": 6, "name": "%alloc_147", "alias_id": 6, "names": ["%alloc_147"], "bytes_scaled": 1216, "size": 1216, "t0": 799, "t1": 882, "start_time": 799, "end_time": 882, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 7, "name": "%alloc_149", "alias_id": 7, "names": ["%alloc_149"], "bytes_scaled": 12352, "size": 12352, "t0": 857, "t1": 1004, "start_time": 857, "end_time": 1004, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 9856, "violated_preference": false}, {"id": 8, "name": "%alloc_122", "alias_id": 8, "names": ["%alloc_122"], "bytes_scaled": 196608, "size": 196608, "t0": 601, "t1": 712, "start_time": 601, "end_time": 712, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 9, "name": "%alloc_146", "alias_id": 9, "names": ["%alloc_146"], "bytes_scaled": 1048576, "size": 1048576, "t0": 807, "t1": 822, "start_time": 807, "end_time": 822, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1216, "violated_preference": false}, {"id": 10, "name": "%alloc_88", "alias_id": 10, "names": ["%alloc_88"], "bytes_scaled": 2099200, "size": 2099200, "t0": 413, "t1": 454, "start_time": 413, "end_time": 454, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4198400, "violated_preference": false}, {"id": 11, "name": "%alloc_123", "alias_id": 11, "names": ["%alloc_123"], "bytes_scaled": 13760, "size": 13760, "t0": 577, "t1": 802, "start_time": 577, "end_time": 802, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099200, "violated_preference": false}, {"id": 12, "name": "%alloc_58", "alias_id": 12, "names": ["%alloc_58"], "bytes_scaled": 2099200, "size": 2099200, "t0": 275, "t1": 314, "start_time": 275, "end_time": 314, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 13, "name": "%alloc_79", "alias_id": 13, "names": ["%alloc_79"], "bytes_scaled": 2099200, "size": 2099200, "t0": 371, "t1": 412, "start_time": 371, "end_time": 412, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 14, "name": "%alloc_53", "alias_id": 14, "names": ["%alloc_53"], "bytes_scaled": 3136, "size": 3136, "t0": 255, "t1": 616, "start_time": 255, "end_time": 616, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6297600, "violated_preference": false}, {"id": 15, "name": "%alloc_196", "alias_id": 15, "names": ["%alloc_196"], "bytes_scaled": 192, "size": 192, "t0": 1165, "t1": 1176, "start_time": 1165, "end_time": 1176, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 16, "name": "%alloc_103", "alias_id": 16, "names": ["%alloc_103"], "bytes_scaled": 196608, "size": 196608, "t0": 481, "t1": 616, "start_time": 481, "end_time": 616, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2112960, "violated_preference": false}, {"id": 17, "name": "%alloc_85", "alias_id": 17, "names": ["%alloc_85"], "bytes_scaled": 2099200, "size": 2099200, "t0": 399, "t1": 440, "start_time": 399, "end_time": 440, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6300736, "violated_preference": false}, {"id": 18, "name": "%alloc", "alias_id": 18, "names": ["%alloc"], "bytes_scaled": 64, "size": 64, "t0": 5, "t1": 300, "start_time": 5, "end_time": 300, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099200, "violated_preference": false}, {"id": 19, "name": "%alloc_70", "alias_id": 19, "names": ["%alloc_70"], "bytes_scaled": 2099200, "size": 2099200, "t0": 329, "t1": 370, "start_time": 329, "end_time": 370, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 0, "violated_preference": false}, {"id": 20, "name": "%alloc_97", "alias_id": 20, "names": ["%alloc_97"], "bytes_scaled": 2099200, "size": 2099200, "t0": 455, "t1": 580, "start_time": 455, "end_time": 580, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4198400, "violated_preference": false}, {"id": 21, "name": "%alloc_100", "alias_id": 21, "names": ["%alloc_100"], "bytes_scaled": 2099200, "size": 2099200, "t0": 469, "t1": 580, "start_time": 469, "end_time": 580, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6300736, "violated_preference": false}, {"id": 22, "name": "%alloc_150", "alias_id": 22, "names": ["%alloc_150"], "bytes_scaled": 114432, "size": 114432, "t0": 819, "t1": 1004, "start_time": 819, "end_time": 1004, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1049792, "violated_preference": false}, {"id": 23, "name": "%alloc_76", "alias_id": 23, "names": ["%alloc_76"], "bytes_scaled": 2099200, "size": 2099200, "t0": 357, "t1": 398, "start_time": 357, "end_time": 398, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099200, "violated_preference": false}, {"id": 24, "name": "%alloc_54", "alias_id": 24, "names": ["%alloc_54"], "bytes_scaled": 3328, "size": 3328, "t0": 247, "t1": 616, "start_time": 247, "end_time": 616, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 8399936, "violated_preference": false}, {"id": 25, "name": "%alloc_2", "alias_id": 25, "names": ["%alloc_2"], "bytes_scaled": 196608, "size": 196608, "t0": 269, "t1": 616, "start_time": 269, "end_time": 616, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 196608, "violated_preference": false}, {"id": 26, "name": "%alloc_64", "alias_id": 26, "names": ["%alloc_64"], "bytes_scaled": 2099200, "size": 2099200, "t0": 301, "t1": 342, "start_time": 301, "end_time": 342, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099200, "violated_preference": false}, {"id": 27, "name": "%alloc_67", "alias_id": 27, "names": ["%alloc_67"], "bytes_scaled": 2099200, "size": 2099200, "t0": 315, "t1": 356, "start_time": 315, "end_time": 356, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4198400, "violated_preference": false}, {"id": 28, "name": "%alloc_61", "alias_id": 28, "names": ["%alloc_61"], "bytes_scaled": 2099200, "size": 2099200, "t0": 287, "t1": 328, "start_time": 287, "end_time": 328, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6300736, "violated_preference": false}, {"id": 29, "name": "%alloc_148", "alias_id": 29, "names": ["%alloc_148"], "bytes_scaled": 6144, "size": 6144, "t0": 813, "t1": 882, "start_time": 813, "end_time": 882, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 0, "violated_preference": false}, {"id": 30, "name": "%alloc_55", "alias_id": 30, "names": ["%alloc_55"], "bytes_scaled": 2099200, "size": 2099200, "t0": 263, "t1": 300, "start_time": 263, "end_time": 300, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 2099264, "violated_preference": false}, {"id": 31, "name": "%alloc_145", "alias_id": 31, "names": ["%alloc_145"], "bytes_scaled": 64, "size": 64, "t0": 723, "t1": 812, "start_time": 723, "end_time": 812, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1049792, "violated_preference": false}, {"id": 32, "name": "%alloc_121", "alias_id": 32, "names": ["%alloc_121"], "bytes_scaled": 393280, "size": 393280, "t0": 601, "t1": 802, "start_time": 601, "end_time": 802, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 196608, "violated_preference": false}, {"id": 33, "name": "%alloc_0", "alias_id": 33, "names": ["%alloc_0"], "bytes_scaled": 101376, "size": 101376, "t0": 793, "t1": 1004, "start_time": 793, "end_time": 1004, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1164224, "violated_preference": false}, {"id": 34, "name": "%alloc_73", "alias_id": 34, "names": ["%alloc_73"], "bytes_scaled": 2099200, "size": 2099200, "t0": 343, "t1": 384, "start_time": 343, "end_time": 384, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 6300736, "violated_preference": false}, {"id": 35, "name": "%alloc_124", "alias_id": 35, "names": ["%alloc_124"], "bytes_scaled": 3072, "size": 3072, "t0": 607, "t1": 802, "start_time": 607, "end_time": 802, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 589888, "violated_preference": false}, {"id": 36, "name": "%alloc_144", "alias_id": 36, "names": ["%alloc_144"], "bytes_scaled": 196608, "size": 196608, "t0": 793, "t1": 822, "start_time": 793, "end_time": 822, "stage": "Preferred", "preferred": "local", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "local", "offset": 6144, "violated_preference": false}, {"id": 37, "name": "%alloc_36", "alias_id": 37, "names": ["%alloc_36"], "bytes_scaled": 3072, "size": 3072, "t0": 149, "t1": 300, "start_time": 149, "end_time": 300, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 4198464, "violated_preference": false}, {"id": 38, "name": "%alloc_82", "alias_id": 38, "names": ["%alloc_82"], "bytes_scaled": 2099200, "size": 2099200, "t0": 385, "t1": 426, "start_time": 385, "end_time": 426, "stage": "Forced", "preferred": "weight", "allowed": ["weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 8403264, "violated_preference": false}, {"id": 39, "name": "%alloc_142", "alias_id": 39, "names": ["%alloc_142"], "bytes_scaled": 786496, "size": 786496, "t0": 713, "t1": 812, "start_time": 713, "end_time": 812, "stage": "Any", "preferred": "any", "allowed": ["local", "weight"], "is_explicit": false, "explicit_offset": null, "explicit_bytes": null, "pool": "weight", "offset": 1265600, "violated_preference": false}], "failures": []};
    const localCap = data.config ? data.config.local_cap : (3*1024*1024);
    const weightCap = data.config ? data.config.weight_cap : (16*1024*1024);
    const LOCAL_MB = (localCap/(1024*1024)).toFixed(0);
    const WEIGHT_MB = (weightCap/(1024*1024)).toFixed(0);

    // State
    let viewRange = 1000;
    let viewOffset = 0;

    // Build pool-specific arrays
    const allocs = (data.allocations || []).map(a => ({
      name: a.name || (a.names && a.names[0]) || ('alloc_'+a.alias_id),
      t0: a.start_time ?? a.t0, t1: a.end_time ?? a.t1,
      size: a.size ?? a.bytes_scaled ?? 0,
      pool: a.pool || 'weight', offset: a.offset || 0,
      violated: !!a.violated_preference, is_explicit: !!a.is_explicit
    }));
    const allocsLocal = allocs.filter(a => a.pool === 'local');
    const allocsWeight = allocs.filter(a => a.pool === 'weight');

    // Elements
    const canvasLocal = document.getElementById('canvasLocal');
    const canvasWeight = document.getElementById('canvasWeight');
    const ctxLocal = canvasLocal.getContext('2d');
    const ctxWeight = canvasWeight.getContext('2d');
    const detailPanel = document.getElementById('detailPanel');

    // Resize canvases based on CSS
    function setupCanvas(canvas) {
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      const w = Math.max(800, rect.width);
      const h = 260;
      canvas.width = w * dpr; canvas.height = h * dpr;
      canvas.style.width = w + 'px'; canvas.style.height = h + 'px';
      const ctx = canvas.getContext('2d');
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    function timeToX(t, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerW = rect.width - 80;
      return 60 + ( (t - viewOffset) / viewRange ) * innerW;
    }
    function addrToY(addr, cap, canvas) {
      const rect = canvas.getBoundingClientRect();
      const innerH = rect.height - 60;
      return rect.height - 30 - (addr / cap) * innerH;
    }

    function drawGrid(ctx, canvas, capMB) {
      const rect = canvas.getBoundingClientRect();
      ctx.clearRect(0,0,rect.width,rect.height);
      ctx.fillStyle = '#fff'; ctx.fillRect(0,0,rect.width,rect.height);
      ctx.strokeStyle = '#e0e0e0'; ctx.lineWidth = 0.5; ctx.setLineDash([2,2]);
      const innerW = rect.width - 80;
      const innerH = rect.height - 60;
      // vertical time grid (10 ticks)
      const step = Math.max(1, Math.floor(viewRange/10));
      for (let t = Math.ceil(viewOffset/step)*step; t <= viewOffset+viewRange; t+=step) {
        const x = 60 + ( (t - viewOffset) / viewRange ) * innerW;
        ctx.beginPath(); ctx.moveTo(x, 20); ctx.lineTo(x, rect.height-20); ctx.stroke();
        ctx.fillStyle = '#666'; ctx.font = '11px Arial'; ctx.textAlign = 'center';
        ctx.fillText(t.toString(), x, rect.height-5);
      }
      // horizontal addr grid: every 1MB
      ctx.setLineDash([1,0]);
      ctx.strokeStyle = '#bdbdbd'; ctx.lineWidth = 1;
      for (let mb=1; mb<=capMB; ++mb) {
        const y = addrToY(mb*1024*1024, capMB*1024*1024, canvas);
        ctx.beginPath(); ctx.moveTo(60, y); ctx.lineTo(rect.width-20, y); ctx.stroke();
      }
      // axes
      ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.setLineDash([]);
      ctx.beginPath(); ctx.moveTo(60, 20); ctx.lineTo(60, rect.height-20); ctx.stroke();
      ctx.beginPath(); ctx.moveTo(60, rect.height-20); ctx.lineTo(rect.width-20, rect.height-20); ctx.stroke();
    }

    function drawPool(ctx, canvas, capBytes, items) {
      drawGrid(ctx, canvas, (capBytes/1024/1024)|0);
      const rect = canvas.getBoundingClientRect();
      items.forEach(a => {
        // skip if out of range
        if (a.t1 < viewOffset || a.t0 > viewOffset+viewRange) return;
        const x1 = Math.max(60, timeToX(a.t0, canvas));
        const x2 = Math.min(rect.width-20, timeToX(a.t1, canvas));
        if (x2 <= x1) return;
        const y = addrToY(a.offset + a.size, capBytes, canvas);
        const h = (a.size / capBytes) * (rect.height - 60);
        const fill = a.is_explicit ? '#FF5722' : (a.violated ? '#FFD700' : '#2196F3');
        ctx.fillStyle = fill;
        ctx.fillRect(x1, y, x2-x1, h);
        ctx.strokeStyle = '#333'; ctx.lineWidth = 1; ctx.strokeRect(x1, y, x2-x1, h);
        // store bounds for hit test
        a._bounds = {x:x1, y:y, w:(x2-x1), h:h, canvas:canvas};
      });
    }

    function renderAll() {
      setupCanvas(canvasLocal); setupCanvas(canvasWeight);
      drawPool(ctxLocal, canvasLocal, localCap, allocsLocal);
      drawPool(ctxWeight, canvasWeight, weightCap, allocsWeight);
      // stats
      document.getElementById('statTotal').textContent = allocs.length.toString();
      document.getElementById('statPV').textContent = allocs.filter(a=>a.violated).length.toString();
      const peakLocal = peakUsage(allocsLocal);
      const peakWeight = peakUsage(allocsWeight);
      document.getElementById('statLocalPeak').textContent = (peakLocal/1024/1024).toFixed(2)+ 'MB';
      document.getElementById('statWeightPeak').textContent = (peakWeight/1024/1024).toFixed(2)+ 'MB';
    }

    function peakUsage(items){
      const ev=[]; items.forEach(a=>{ ev.push({t:a.t0, d:+a.size}); ev.push({t:a.t1, d:-a.size}); });
      ev.sort((x,y)=>x.t-y.t); let cur=0, peak=0; ev.forEach(e=>{cur+=e.d; if(cur>peak) peak=cur;}); return peak;
    }

    function hitTest(canvas, x, y) {
      const list = (canvas===canvasLocal) ? allocsLocal : allocsWeight;
      for (let a of list) {
        if (!a._bounds) continue;
        const b=a._bounds; if (x>=b.x && x<=b.x+b.w && y>=b.y && y<=b.y+b.h) return a;
      }
      return null;
    }

    function showDetail(a){
      const mb=(a.size/1024/1024).toFixed(3);
      detailPanel.innerHTML = `
        <div><b>${a.name}</b></div>
        <div>Pool: ${a.pool}</div>
        <div>Time: [${a.t0} - ${a.t1}]</div>
        <div>Offset: ${a.offset} (bytes)</div>
        <div>Size: ${mb} MB</div>
        <div>Explicit: ${a.is_explicit}</div>
        <div>Pref. Violated: ${a.violated}</div>
      `;
    }

    function onCanvasClick(ev, canvas){
      const rect = canvas.getBoundingClientRect();
      const x = ev.clientX - rect.left; const y = ev.clientY - rect.top;
      const a = hitTest(canvas, x, y); if (a) showDetail(a);
    }

    // UI bindings
    document.getElementById('viewRange').addEventListener('input', e=>{
      viewRange = parseInt(e.target.value); document.getElementById('viewRangeValue').textContent=viewRange; renderAll();
    });
    document.getElementById('viewOffset').addEventListener('input', e=>{
      viewOffset = parseInt(e.target.value); document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    document.getElementById('resetBtn').addEventListener('click', ()=>{
      viewRange=1000; viewOffset=0; document.getElementById('viewRange').value=viewRange; document.getElementById('viewRangeValue').textContent=viewRange; document.getElementById('viewOffset').value=viewOffset; document.getElementById('viewOffsetValue').textContent=viewOffset; renderAll();
    });
    canvasLocal.addEventListener('click', ev=> onCanvasClick(ev, canvasLocal));
    canvasWeight.addEventListener('click', ev=> onCanvasClick(ev, canvasWeight));

    // Initial
    window.addEventListener('resize', renderAll);
    renderAll();
  </script>
</body>
</html>
