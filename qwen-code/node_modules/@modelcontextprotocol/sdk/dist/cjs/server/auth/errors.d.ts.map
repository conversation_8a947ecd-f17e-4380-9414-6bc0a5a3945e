{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D;;GAEG;AACH,qBAAa,UAAW,SAAQ,KAAK;aAKjB,QAAQ,CAAC,EAAE,MAAM;IAJnC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;gBAGvB,OAAO,EAAE,MAAM,EACC,QAAQ,CAAC,EAAE,MAAM,YAAA;IAMnC;;OAEG;IACH,gBAAgB,IAAI,kBAAkB;IAatC,IAAI,SAAS,IAAI,MAAM,CAEtB;CACF;AAED;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,UAAU;IACjD,MAAM,CAAC,SAAS,SAAqB;CACtC;AAED;;;GAGG;AACH,qBAAa,kBAAmB,SAAQ,UAAU;IAChD,MAAM,CAAC,SAAS,SAAoB;CACrC;AAED;;;;GAIG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC/C,MAAM,CAAC,SAAS,SAAmB;CACpC;AAED;;;GAGG;AACH,qBAAa,uBAAwB,SAAQ,UAAU;IACrD,MAAM,CAAC,SAAS,SAAyB;CAC1C;AAED;;;GAGG;AACH,qBAAa,yBAA0B,SAAQ,UAAU;IACvD,MAAM,CAAC,SAAS,SAA4B;CAC7C;AAED;;;GAGG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC/C,MAAM,CAAC,SAAS,SAAmB;CACpC;AAED;;GAEG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC/C,MAAM,CAAC,SAAS,SAAmB;CACpC;AAED;;;GAGG;AACH,qBAAa,WAAY,SAAQ,UAAU;IACzC,MAAM,CAAC,SAAS,SAAkB;CACnC;AAED;;;GAGG;AACH,qBAAa,2BAA4B,SAAQ,UAAU;IACzD,MAAM,CAAC,SAAS,SAA6B;CAC9C;AAED;;;GAGG;AACH,qBAAa,4BAA6B,SAAQ,UAAU;IAC1D,MAAM,CAAC,SAAS,SAA+B;CAChD;AAED;;;GAGG;AACH,qBAAa,yBAA0B,SAAQ,UAAU;IACvD,MAAM,CAAC,SAAS,SAA4B;CAC7C;AAED;;;GAGG;AACH,qBAAa,iBAAkB,SAAQ,UAAU;IAC/C,MAAM,CAAC,SAAS,SAAmB;CACpC;AAED;;;GAGG;AACH,qBAAa,qBAAsB,SAAQ,UAAU;IACnD,MAAM,CAAC,SAAS,SAAwB;CACzC;AAED;;;GAGG;AACH,qBAAa,oBAAqB,SAAQ,UAAU;IAClD,MAAM,CAAC,SAAS,SAAuB;CACxC;AAED;;;GAGG;AACH,qBAAa,0BAA2B,SAAQ,UAAU;IACxD,MAAM,CAAC,SAAS,SAA6B;CAC9C;AAED;;GAEG;AACH,qBAAa,sBAAuB,SAAQ,UAAU;IACpD,MAAM,CAAC,SAAS,SAAwB;CACzC;AAED;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAClC,OAAO,CAAC,QAAQ,CAAC,eAAe;gBAAf,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM;IAIxF,IAAI,SAAS,IAAI,MAAM,CAEtB;CACF;AAED;;GAEG;AACH,eAAO,MAAM,YAAY;;CAiBf,CAAC"}