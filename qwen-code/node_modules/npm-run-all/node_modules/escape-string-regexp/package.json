{"name": "escape-string-regexp", "version": "1.0.5", "description": "Escape RegExp special characters", "license": "MIT", "repository": "sindresorhus/escape-string-regexp", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbna.nl)"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["escape", "regex", "regexp", "re", "regular", "expression", "string", "str", "special", "characters"], "devDependencies": {"ava": "*", "xo": "*"}}